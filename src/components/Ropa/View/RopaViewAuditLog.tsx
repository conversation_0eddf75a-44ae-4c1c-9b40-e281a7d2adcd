import { Download } from 'lucide-react';
import * as React from 'react';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { But<PERSON> } from '../../../@/components/ui/Common/Elements/Button/ButtonSort';
import { Dialog, DialogContent } from '../../../@/components/ui/Common/Elements/Dialog/Dialog';
import { SkeletonCard } from '../../../@/components/ui/Common/Elements/Skeleton/Skeleton';
import httpClient from '../../../api/httpClient';
import download from '../../../assets/downloadedTransition.svg';
import greyCircle from '../../../assets/GreyEllipse.svg';
import { activityLogActions } from '../../../redux/reducers/ActivityLog/ActivityLogSlice';
import { getInitialsByName } from '../../../utils/helperData';
import { DOWNLOAD_ROPA_AUDIT_LOG } from '../../common/api';
import AvatarFrame from '../../common/Avatar';
import { convertDateToHumanView } from '../../common/CommonHelperFunctions';
import TablePaginationDemo from '../../common/Pagenation';
import { Audit_Log_Ropa } from '../../common/services/auditLog';

interface AuditData {
  id: number;
  action: string;
  createdAt: string;
  name: string;
}

interface AuditResponse {
  count: number;
  rows: AuditData[];
}

interface RopaAuditLog {
  id: number;
}

const RopaViewAuditLog: React.FC<RopaAuditLog> = ({ id }) => {
  console.log('id:', id);
  const { t } = useTranslation();
  const [isLoading, setLoading] = useState(false);
  const dispatch = useDispatch();
  const count = useSelector((state: any) => state.activityLog.pagination.count);
  const numberOfItems = useSelector((state: any) => state.activityLog.pagination.itemsPerPage);
  const currentPage = useSelector((state: any) => state.activityLog.pagination.page);
  const [auditData, setAuditData] = useState<AuditResponse | null>(null);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState<boolean>(false);

  function handleNumberOfPages(value: number) {
    dispatch(activityLogActions.setRopaViewAuditLogPaginationItemsPerPage(value));
  }

  function handlePageChange(value: number) {
    dispatch(activityLogActions.setRopaViewAuditLogPaginationPage(value));
  }

  useEffect(() => {
    const fetchAuditLog = async () => {
      setLoading(true);
      if (id) {
        dispatch(activityLogActions.setRopaViewAuditLogPaginationPage(1));
      }
      const response = await Audit_Log_Ropa(currentPage, numberOfItems, id);
      if (response?.status) {
        setAuditData(response?.data?.result);
        dispatch(
          activityLogActions.setRopaViewAuditLogPaginationCount(response?.data?.result?.count)
        );
      }
      setLoading(false);
    };

    fetchAuditLog();
  }, [currentPage, numberOfItems, id]);

  const handleDownloadAuditLog = async () => {
    const request_id = id;
    try {
      toast.loading(t('CommonErrorMessages.ProcessingEllipsis'));
      const response = await httpClient.get(`${DOWNLOAD_ROPA_AUDIT_LOG}/${request_id}`, {
        responseType: 'blob',
      });

      if (response?.status) {
        toast.dismiss();
        setIsSuccessModalOpen(true);
      }
    } catch (error) {
      console.error('Enexpected error: ', error);
      throw error;
    }
  };

  return (
    // <div className="flex flex-col px-3 py-3 text-base text-black bg-white max-md:pl-5">
    <div style={{ height: 'calc(100vh - 16.5rem)' }}>
      <div className="flex size-full shrink-0 flex-col gap-3 overflow-auto px-5 scrollbar-hide">
        <div className="flex justify-end p-2">
          <Button variant="outline" className="w-46 h-10" onClick={() => handleDownloadAuditLog()}>
            Downlaod
            <Download className="ml-2 h-4 w-4" />
          </Button>
        </div>
        <div>
          {isSuccessModalOpen && (
            <Dialog open={isSuccessModalOpen} onOpenChange={setIsSuccessModalOpen}>
              <DialogContent className="max-w-sm p-5">
                <div className="flex w-full flex-row items-center justify-end">
                  {/* <Button
                    size="icon"
                    variant="ghost"
                    className="bg-[#E3E3E3] size-6 p-0 hover:bg-[#d1d1d1]"
                    onClick={() => setIsSuccessModalOpen(false)}
                  >
                    <X className="h-4 w-4 text-black" />
                  </Button> */}
                </div>

                <div className="flex flex-col items-center justify-center gap-3">
                  <img src={download} alt="download" className="aspect-square w-18" />
                  <div className="flex flex-col items-center justify-center gap-2.5">
                    <p className="text-center font-primary-text text-3xl font-semibold leading-normal text-quaternary-text">
                      Downloaded
                    </p>
                    <p className="text-center font-primary-text text-xs font-normal leading-normal text-[#686868]">
                      The download link has been sent to your email address. Please check your
                      inbox.
                    </p>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </div>
        {isLoading ? (
          <>
            <SkeletonCard />
            <SkeletonCard />
            <SkeletonCard />
            <SkeletonCard />
            <SkeletonCard />
          </>
        ) : (
          auditData?.rows.map((row) => (
            <div
              key={row.id}
              className="flex w-full flex-row items-center rounded-lg border border-[#C0CDE0] bg-white px-[6px] py-[7px] text-base leading-normal text-[#999] lg:px-[13px]"
            >
              <div className="flex w-[25rem] items-center gap-1 p-2.5 lg:gap-2.5">
                <AvatarFrame value={row?.name} getInitials={getInitialsByName} />
                {row.name}
              </div>
              <div className="flex w-[50rem] items-center gap-1 p-2.5 lg:gap-2.5">
                <p className="font-medium">{row.action}</p>
              </div>
              <div className="flex w-60 items-center gap-1 p-2.5 lg:gap-2.5">
                {/* {new Date(row.createdAt).toLocaleString()} */}
                <img src={greyCircle} alt="Grey Circle Dot" />
                {convertDateToHumanView(row.createdAt)}
              </div>
              <div className="flex w-60 items-center gap-1 p-2.5 lg:gap-2.5">
                {/* {new Date(row.createdAt).toLocaleString()} */}
                <img src={greyCircle} alt="Grey Circle Dot" />
                {new Date(row?.createdAt).toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </div>
            </div>
          ))
        )}
        {auditData?.rows?.length === 0 ? (
          <div className="flex flex-row justify-center">No data found</div>
        ) : (
          <></>
        )}
        {/* <div>{auditData?.rows.length? "No data found" : ""}</div> */}
        {/* </div> */}
        {/* </div> */}
      </div>
      <TablePaginationDemo
        count={count}
        handleItemsChange={handleNumberOfPages}
        handlePageChange={handlePageChange}
        currentPage={currentPage}
        numOfItems={numberOfItems}
      />
    </div>
  );
};

export default RopaViewAuditLog;
