import * as React from 'react';
// import { TrendingUp } from 'lucide-react';
import { Label, Pie, Pie<PERSON><PERSON> } from 'recharts';

// import {
//   Card,
//   CardContent,
//   CardDescription,
//   CardFooter,
//   CardHeader,
//   CardTitle,
// } from '../../../@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  // ChartLegend,
  // ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from '../../../@/components/ui/chart';

// export const description = 'A donut chart with text';

// const chartData = [
//   { key: 'chrome', value: 275, fill: 'var(--color-chrome)' },
//   { key: 'safari', value: 200, fill: 'var(--color-safari)' },
//   { key: 'firefox', value: 287, fill: 'var(--color-firefox)' },
//   { key: 'edge', value: 173, fill: 'var(--color-edge)' },
//   { key: 'other', value: 190, fill: 'var(--color-other)' },
// ];

// const chartConfig = {
//   visitors: {
//     label: 'Visitors',
//   },
//   chrome: {
//     label: 'Chrome',
//     color: 'hsl(var(--chart-1))',
//   },
//   safari: {
//     label: 'Safari',
//     color: 'hsl(var(--chart-2))',
//   },
//   firefox: {
//     label: 'Firefox',
//     color: 'hsl(var(--chart-3))',
//   },
//   edge: {
//     label: 'Edge',
//     color: 'hsl(var(--chart-4))',
//   },
//   other: {
//     label: 'Other',
//     color: 'hsl(var(--chart-5))',
//   },
// } satisfies ChartConfig;

interface ChartData {
  key: string | number;
  value: number;
  fill: string;
}

interface DonutWithTextProperties {
  chartConfig: ChartConfig;
  chartData: ChartData[];
  chartSubtext?: string;
  totalValue: number;
}

const DonutWithText: React.FC<DonutWithTextProperties> = ({
  chartConfig,
  chartData,
  chartSubtext,
  totalValue,
}) => {
  // const totalValue = React.useMemo(() => {
  //   return chartData.reduce(
  //     (accumulator, current) => Number(accumulator) + Number(current?.value),
  //     0
  //   );
  // }, []);

  return (
    <ChartContainer config={chartConfig} className="mx-auto aspect-square max-h-[250px]">
      <PieChart>
        <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
        <Pie data={chartData} dataKey="value" nameKey="key" innerRadius={60} strokeWidth={5}>
          <Label
            content={({ viewBox }) => {
              if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                return (
                  <text x={viewBox.cx} y={viewBox.cy} textAnchor="middle" dominantBaseline="middle">
                    <tspan
                      x={viewBox.cx}
                      y={viewBox.cy}
                      className="fill-foreground text-3xl font-bold"
                    >
                      {totalValue.toLocaleString()}
                    </tspan>
                    {chartSubtext && chartSubtext !== '' ? (
                      <tspan
                        x={viewBox.cx}
                        y={(viewBox.cy || 0) + 24}
                        className="fill-muted-foreground"
                      >
                        {chartSubtext}
                      </tspan>
                    ) : (
                      <></>
                    )}
                  </text>
                );
              }
            }}
          />
        </Pie>
        {/* <ChartLegend
          content={<ChartLegendContent nameKey="key" />}
          className="-translate-y-2 flex-wrap gap-2 [&>*]:basis-1/4 [&>*]:justify-center"
        /> */}
      </PieChart>
    </ChartContainer>
    // <Card className="flex flex-col">
    //   <CardHeader className="items-center pb-0">
    //     <CardTitle>Pie Chart - Donut with Text</CardTitle>
    //     <CardDescription>January - June 2024</CardDescription>
    //   </CardHeader>
    //   <CardContent className="flex-1 pb-0">
    //   </CardContent>
    //   <CardFooter className="flex-col gap-2 text-sm">
    //     <div className="flex items-center gap-2 font-medium leading-none">
    //       Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
    //     </div>
    //     <div className="leading-none text-muted-foreground">
    //       Showing total visitors for the last 6 months
    //     </div>
    //   </CardFooter>
    // </Card>
  );
};

export default DonutWithText;
