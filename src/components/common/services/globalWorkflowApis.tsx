import axios from 'axios';
import Cookies from 'js-cookie';
import toast from 'react-hot-toast';
import httpClient from '../../../api/httpClientNew';
import { AddCategoryFormData, WorkflowStepSubmit } from '../../../types/global-workflow';
import { api_key_work as api_key } from '../../../utils/helperData';
import {
  ACTIVITY_MAIL_TEMPLATES,
  ADD_DSR_CONTROL,
  ADD_TASK_TASKOVERVIEW,
  APPROVE_REJECTED_REQUEST,
  ARCHIVE_DSR_REQUEST,
  ASSIGNED_TASK_LIST,
  ASSIGN_DSR_REQUEST,
  CHECK_STATUS,
  COMMON_ADD_TASK,
  COMMON_ADD_WORKFLOW,
  COMMON_ADD_WORKFLOW_STEPS,
  COMMON_DELETE_WORKFLOW_STEP,
  COMMON_EDIT_WORKFLOW,
  COMMON_ENDPOINT,
  COMMON_FETCH_TASK_LIST,
  COMMON_FETCH_WORKFLOW_LIST_BY_ID,
  COMMON_MOVEDOWN_WORKFLOW_STEP,
  COMMON_MOVEUP_WORKFLOW_STEP,
  COMMON_SELECTED_WORKFLOW_AUTOMATION,
  COMMON_UPDATE_TASK,
  COMMON_UPDATE_WORKFLOW_TASK,
  COUNT_CHAT_ACTIVITY_LOG,
  CREATE_CATEGORY_DSR_FORM_BUILDER,
  CREATE_DSR_FORM_BUILDER,
  CREATE_REQUEST_FORM,
  DELETE_DOCUMENT_DSR,
  DELETE_DSR_FORM_BUILDER,
  DELETE_DSR_FORM_BUILDER_QUESTION,
  DELETE_EMAIL_TEMPLATE,
  DELETE_FORM_TRANSLATION,
  DELETE_WORKFLOW_STEP,
  DOWNLOAD_AUDIT_LOG,
  EMAIL_TEMPLATE,
  FETCH_ASSIGNEE_DSR_LIST,
  FETCH_ASSIGNEE_VIEW_DETAILS_LIST,
  FETCH_BASIC_INFO_CONTROL,
  FETCH_COUNTRIES,
  FETCH_DASHBOARD_REQUEST_BY_OWNER,
  FETCH_DASHBOARD_REQUEST_BY_PENDING,
  FETCH_DASHBOARD_REQUEST_BY_RESIDENCY,
  FETCH_DASHBOARD_REQUEST_BY_RIGHTS,
  FETCH_DASHBOARD_REQUEST_BY_STAGE,
  FETCH_DASHBOARD_REQUEST_BY_STATICS,
  FETCH_DASHBOARD_REQUEST_COUNT,
  FETCH_DSR_ALL_AUDIT_LOG,
  FETCH_DSR_FORM_BUILDER_CATEGORY,
  FETCH_FORM,
  FETCH_FORM_BUILDER_LIST,
  FETCH_GUEST_REQUEST_ALL_DATA,
  FETCH_LANGUAGES_LIST,
  FETCH_MAIL_ACTIVITY_LOG,
  FETCH_PREVIEW_REQUEST_DATA,
  FETCH_PREVIEW_REQUEST_DATA_GUEST,
  FETCH_REQUEST_ALL_DATA,
  FETCH_REQUEST_LIST,
  FETCH_REQUEST_TYPE,
  FETCH_STATE,
  FETCH_TRANSLATED_CONTENT,
  FETCH_TRANSLATED_LANGUAGE,
  GET_DSR_FORM_BUILDER_DATA,
  GUEST_REQUESTS,
  PUBLISH_DSR_FORM,
  REQUESTG_PER_OWNER,
  REQUEST_DETAILS,
  SEND_OTP_FORM_BUILDER,
  SEND_OTP_GUEST_LOGIN,
  SUBMIT_FORM_BUILDER,
  UNARCHIVE_DSR_REQUEST,
  UPDATE_BUSINESS_Unit,
  UPDATE_DSR_CONTROLS,
  UPDATE_DSR_FORM_CONTENT,
  UPDATE_DSR_LOGO,
  UPDATE_EMAIL_TEMPLATE,
  UPDATE_FORM_BUILDER_CONTENT,
  UPDATE_FORM_BUILDER_ORDER,
  UPLOAD_ADD_TASK_ATTACHMENT,
  UPLOAD_GUEST_REQUESTS,
  UPLOAD_LETTER_OF_AUTHORITY,
  UPLOAD_REQUEST_IDENTIFICATION,
  UPLOAD_ROPA_AI__ATTACHMENT,
  UPLOAD_dOCUMENT,
  VERIFY_OTP_FORM,
  VERIFY_OTP_GUEST_LOGIN,
} from '../api';
import { getAccessToken } from './token-encryption';

interface FormData {
  id?: number;
  stage_id: number | undefined;
  title: string;
  // department: string;
  // startDate: Date;
  // dueDate: Date;
  guidance_text: string;
  // files: File[];
  documents?: [];
  activepieces_automation_id?: string | null;
}

interface FormDatas {
  id?: number;
  stage_id: number | undefined;
  title: string;
  guidance_text: string;
  workflow_id?: string;
  assignee_id: string[];
  documents?: [];
  activepieces_automation_id?: string | null;
  due_date?: Date | string;
  start_date?: Date | string;
  due_days: number;
  task_type: string;
}

interface AutomationInterface {
  activepieces_automation_id: string | null;
}

export interface EmailTemplateUpdatePayload {
  subject?: string;
  content?: string;
  headerColor?: string;
  logoUrl?: string;
}
interface UploadResponse {
  success: boolean;
  status_code: number;
  message: string;
  result: any[];
  time: number;
}

export interface FormRetention {
  logoUrl: string;
  headerBackgroundColor: string;
  textSize: string;
  fontFamily: string;
  fontColor: string;
  paragraphContent: string;
  headerTextColor: string;
}
interface FormBuilder {
  // formId: string;
  // formName: string;
  // customerId: number | undefined;
  content: {
    logoUrl: string;
    headerBackgroundColor: string;
    textSize: string;
    fontFamily: string;
    fontColor: string;
    paragraphContent: string;
    headerTextColor: string;
  };
  // questions: any[];
}

export interface CreateRequestFormData {
  dsr_id: string;
  dsr_request_type_id: number; // Ensure this is a number
  is_data_subject: boolean;
  description: string;
  relationship: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_no: string;
  dob: string;
  unique_identification_type: string;
  unique_identification_number: string;
  address_1: string;
  address_2?: string;
  city: string;
  state_id: number;
  country_id: number;
  postal_code: number;
  dsr_return_preference: string;
  is_internal_request: boolean;
  third_party_name?: string;
  third_party_practice_name?: string;
  third_party_email?: string;
  third_party_contact_number?: string;
  joint_party_details?: {
    first_name: string;
    last_name: string;
    email: string;
    phone_no: string;
    dob: string;
    unique_identification_type: string;
    unique_identification_number: string;
    address_1: string;
    address_2?: string;
    city: string;
    state_id: number;
    country_id: number;
    postal_code: number;
  };
  second_address_1: string;
  second_address_2: string;
  second_city: string;
  second_state_id: number;
  second_country_id: number;
  second_postal_code: number;
  uploadDocuments: boolean;
  uploadAuthorityDocuments: boolean;
}

interface UpdateWorkflowAutomationPayload {
  activepieces_automation_id: string | null;
  step_title: string;
}

var token = getAccessToken();
export const fetchWorkflowList = async (page = 1, size = 10, searchTerm = '') => {
  try {
    const searchParam = searchTerm ? `&search=${encodeURIComponent(searchTerm)}` : '';
    const response = await httpClient.get(
      `${COMMON_ADD_WORKFLOW}?page=${page}&size=${size}${searchParam}`,
      {}
    );
    return response;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const fetchWorkflowListById = async (id: number) => {
  try {
    const response = await httpClient.get(`${COMMON_FETCH_WORKFLOW_LIST_BY_ID}${id}`, {});
    return response;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};
export const fetchTaskList = async (id: string) => {
  try {
    const response = await httpClient.get(`${COMMON_FETCH_TASK_LIST}${id}?page=1&size=5`, {});
    return response;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const addWorkflow = async (flowtype: string, group_id: any, module_name: string) => {
  try {
    const response = await httpClient.post(
      COMMON_ADD_WORKFLOW,
      { flowtype, group_id, module_name },
      {}
    );
    return response.data;
  } catch (error) {
    // if (httpClient.ishttpClientError(error)) {
    //   toast.dismiss();
    //   toast.error(error.response?.data?.message);
    // } else {
    toast.dismiss();
    toast.error('Failed to add workflow.');
    // }
  }
};

export const addTask = async (data: FormData) => {
  console.log('testform', data);

  try {
    const response = await httpClient.post(COMMON_ADD_TASK, data, {});
    return response.data;
  } catch (error) {
    // if (httpClient.ishttpClientError(error)) {
    //   toast.dismiss();
    //   toast.error(error.response?.data?.message);
    // } else {
    toast.dismiss();
    toast.error('Failed To Add Task');
    // }
  }
};

export const updateWorkflowTask = async (currentTaskId: number, data: FormDatas) => {
  console.log('testform', data);

  try {
    const response = await httpClient.patch(`${COMMON_UPDATE_TASK}/${currentTaskId}`, data, {});
    return response.data;
  } catch (error) {
    // if (httpClient.ishttpClientError(error)) {
    //   toast.dismiss();
    //   toast.error(error.response?.data?.message);
    // } else {
    toast.dismiss();
    toast.error('Failed to update task.');
    // }
  }
};

export const updateTask = async (currentTaskId: number, updateData: AutomationInterface) => {
  try {
    const response = await httpClient.patch(
      `${COMMON_UPDATE_WORKFLOW_TASK}/${currentTaskId}`,
      updateData,
      {}
    );
    return response.data;
  } catch (error) {
    console.error('Failed to add task', error);
    toast.dismiss();
    toast.error('Failed to update task');
  }
};

// export const addTaskOverview = async (data: FormData) => {
//   console.log('testform', data);

//   try {
//     const response = await httpClient.post(ADD_TASK_TASKOVERVIEW, data, {});
//     return response.data;
//   } catch (error) {
//     toast.dismiss();
//     toast.error('Failed to add task');
//   }
// };

// export const updateTaskOverview = async (data: FormData, id: number) => {
//   const URL = `${ADD_TASK_TASKOVERVIEW}/${id}`;
//   const formData = { ...data };
//   if (formData.id != undefined) delete formData?.id;
//   try {
//     const response = await httpClient.patch(URL, formData, {});
//     return response.data;
//   } catch (error) {
//     toast.dismiss();
//     toast.error('Failed to update task.');
//   }
// };

export const updateBusinessUnit = async (id: number, business_unit: string) => {
  try {
    const response = await httpClient.patch(`${UPDATE_BUSINESS_Unit}/${id}`, { business_unit }, {});
    return response.data;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to update business unit.');
  }
};

export const editWorkflow = async (
  id: number,
  flowtype: string,
  workflow_status: string,
  group_id: string
) => {
  try {
    const response = await httpClient.patch(
      `${COMMON_EDIT_WORKFLOW}/${id}`,
      {
        flowtype,
        workflow_status,
        group_id,
      },
      {}
    );
    return response.data;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to edit workflow.');
  }
};

export const fetchPendingRequests = async (page = 1, size = 10, searchTerm: string = '') => {
  try {
    const response = await httpClient.get(
      `${FETCH_REQUEST_LIST}?page=${page}&size=${size}&search=${encodeURIComponent(searchTerm)}`,
      {}
    );
    return response.data;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const fetchApprovedRequests = async (
  page = 1,
  size = 10,
  status: string = 'APPROVED',
  searchTerm: string = ''
) => {
  try {
    const response = await httpClient.get(
      `${FETCH_REQUEST_LIST}?page=${page}&size=${size}&status=${status}&search=${encodeURIComponent(searchTerm)}`,
      {}
    );
    return response.data;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const fetchCompleteRequests = async (
  page = 1,
  size = 10,
  status: string = 'COMPLETED',
  searchTerm: string = ''
) => {
  try {
    const response = await httpClient.get(
      `${FETCH_REQUEST_LIST}?page=${page}&size=${size}&status=${status}&search=${encodeURIComponent(searchTerm)}`,
      {}
    );
    return response.data;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};
export const fetchArchiveRequests = async (
  page = 1,
  size = 10,
  status: string = 'ARCHIVED',
  searchTerm: string = ''
) => {
  try {
    const response = await httpClient.get(
      `${FETCH_REQUEST_LIST}?page=${page}&size=${size}&status=${status}&search=${encodeURIComponent(searchTerm)}`,
      {}
    );
    return response.data;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};
export const fetchRejectInProgress = async (
  page = 1,
  size = 10,
  status: string = 'REJECTED_IN_PROGRESS',
  searchTerm: string = ''
) => {
  try {
    const response = await httpClient.get(
      `${FETCH_REQUEST_LIST}?page=${page}&size=${size}&status=${status}&search=${encodeURIComponent(searchTerm)}`,
      {}
    );
    return response.data;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const fetchRejectedRequests = async (
  page = 1,
  size = 10,
  status: string = 'REJECTED',
  searchTerm: string = ''
) => {
  try {
    const response = await httpClient.get(
      `${FETCH_REQUEST_LIST}?page=${page}&size=${size}&status=${status}&search=${encodeURIComponent(searchTerm)}`,
      {}
    );
    return response.data;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};
//Request details

export const fetchStepWiseAuditLog = async (url: string) => {
  try {
    const response = await httpClient.get(url, {});
    return response.data;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const fetchAllAudit = async (id: number) => {
  try {
    const response = await httpClient.get(`${FETCH_DSR_ALL_AUDIT_LOG}/${id}`, {});
    return response.data.result;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const fetchRequestDetails = async (request_id: number) => {
  console.log('url-assignee', REQUEST_DETAILS);
  try {
    const response = await httpClient.get(`${REQUEST_DETAILS}/${request_id}`, {});
    return response.data;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const fetchAllDataRequest = async (id: number) => {
  try {
    const response = await httpClient.get(`${FETCH_REQUEST_ALL_DATA}/${id}`, {});
    console.log(response.data.result, 'Fetched Data');
    return response.data.result;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const fetchPreviewData = async (id: number) => {
  try {
    const response = await httpClient.get(`${FETCH_PREVIEW_REQUEST_DATA}/${id}`, {});
    console.log(response.data.result.formDetails, 'Fetched Data');
    return response.data.result;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const createWorkflowSteps = async (data: WorkflowStepSubmit) => {
  console.log('test99', data);

  try {
    const response = await httpClient.post(
      COMMON_ADD_WORKFLOW_STEPS,
      {
        type_id: data.type_id,
        step_title: data.step_title,
        guidance_text: data.guidance_text ? data.guidance_text : undefined,
      },
      {
        headers: {
          'x-api-key': api_key,
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to create new step.');
  }
};

export const fetchRequestsType = async () => {
  try {
    const response = await httpClient.get(`${FETCH_REQUEST_TYPE}`, {});
    return response.data.result;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const fetchCountries = async () => {
  try {
    const response = await httpClient.get(`${FETCH_COUNTRIES}`, {});
    return response.data.result.rows;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const fetchState = async (country_id: number) => {
  try {
    const response = await httpClient.get(`${FETCH_STATE}?country_id=${country_id}`, {});
    return response.data.result.rows;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const createRequest = async (data: CreateRequestFormData) => {
  console.log('Submitting request data:', data);

  try {
    const response = await httpClient.post(CREATE_REQUEST_FORM, data, {});

    return response.data;
  } catch (error: any) {
    console.error('Failed to create request:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'Failed to create request.');
  }
};

export const uploadLogo = async (file: File): Promise<string> => {
  const formData = new FormData();
  formData.append('logo', file);

  try {
    const response = await httpClient.post(UPDATE_DSR_LOGO, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data.logoUrl;
  } catch (error) {
    console.error('Error in uploading logo:', error);
    throw error;
  }
};

export const submitFormContent = async (formData: FormRetention): Promise<any> => {
  try {
    const response = await httpClient.post(
      UPDATE_DSR_FORM_CONTENT,
      { formData },
      {
        headers: {
          'x-api-key': api_key,
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response;
  } catch (error) {
    console.error('Error in submitting form:', error);
    throw error;
  }
};

export const fetchStatsData = async (business_unit: number) => {
  try {
    if (business_unit === -1) {
      const response = await httpClient.get(FETCH_DASHBOARD_REQUEST_COUNT, {});
      console.log('Data fetched stats:', response.data);
      return response.data;
    } else {
      const response = await httpClient.get(
        `${FETCH_DASHBOARD_REQUEST_COUNT}?business_unit=${business_unit}`,
        {}
      );
      console.log('Data fetched stats:', response.data);
      return response.data;
    }
  } catch (error) {
    console.error('Error in fetching stats data:', error);
    throw error;
  }
};

export const fetchFormData = async () => {
  try {
    const response = await httpClient.get(FETCH_FORM, {});
    return response.data;
  } catch (error) {
    console.error('Error in fetching stats data:', error);
    throw error;
  }
};

export const fetchByRightsData = async (filtering: string, business_unit: number) => {
  try {
    if (business_unit === -1) {
      const response = await httpClient.get(
        `${FETCH_DASHBOARD_REQUEST_BY_RIGHTS}?filter=${filtering}`,
        {}
      );
      console.log('Data fetched from Right:', response.data);
      return response.data;
    } else {
      const response = await httpClient.get(
        `${FETCH_DASHBOARD_REQUEST_BY_RIGHTS}?filter=${filtering}&business_unit=${business_unit}`,
        {}
      );
      console.log('Data fetched from Right:', response.data);
      return response.data;
    }
  } catch (error) {
    console.error('Error in fetching stats data:', error);
    throw error;
  }
};

export const fetchStaticsData = async (filter: string, business_unit: number) => {
  try {
    if (business_unit === -1) {
      const response = await httpClient.get(
        `${FETCH_DASHBOARD_REQUEST_BY_STATICS}?filter=${filter}`,
        {}
      );
      console.log('Data fetched from statics:', response.data.result);
      return response.data.result;
    } else {
      const response = await httpClient.get(
        `${FETCH_DASHBOARD_REQUEST_BY_STATICS}?filter=${filter}&business_unit=${business_unit}`,
        {}
      );
      console.log('Data fetched from statics:', response.data.result);
      return response.data.result;
    }
  } catch (error) {
    console.error('Error in fetching stats data:', error);
    throw error;
  }
};

export const fetchByPending = async (business_unit: number) => {
  try {
    if (business_unit === -1) {
      const response = await httpClient.get(`${FETCH_DASHBOARD_REQUEST_BY_PENDING}`, {});
      console.log('Data fetched from pending:', response.data);
      return response.data;
    } else {
      const response = await httpClient.get(
        `${FETCH_DASHBOARD_REQUEST_BY_PENDING}?business_unit=${business_unit}`,
        {}
      );
      console.log('Data fetched from pending:', response.data);
      return response.data;
    }
  } catch (error) {
    console.error('Error in fetching stats data:', error);
    throw error;
  }
};

export const fetchByStage = async (request_type: string, days: number, business_unit: number) => {
  try {
    if (business_unit === -1) {
      const response = await httpClient.get(
        `${FETCH_DASHBOARD_REQUEST_BY_STAGE}?request_type=${request_type}&days=${days}`,
        {}
      );
      console.log('Data fetched from stage:', response.data);
      return response.data;
    } else {
      const response = await httpClient.get(
        `${FETCH_DASHBOARD_REQUEST_BY_STAGE}?request_type=${request_type}&days=${days}&business_unit=${business_unit}`,
        {}
      );
      console.log('Data fetched from stage:', response.data);
      return response.data;
    }
  } catch (error) {
    console.error('Error in fetching stats data:', error);
    throw error;
  }
};

export const fetchByOwner = async () => {
  try {
    const response = await httpClient.get(FETCH_DASHBOARD_REQUEST_BY_OWNER, {});
    console.log('Data fetched from owber:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching stats data:', error);
    throw error;
  }
};

export const fetchByResidency = async (business_unit: number) => {
  try {
    if (business_unit === -1) {
      const response = await httpClient.get(`${FETCH_DASHBOARD_REQUEST_BY_RESIDENCY}`, {});
      console.log('Data fetched from residency:', response.data);
      return response.data;
    } else {
      const response = await httpClient.get(
        `${FETCH_DASHBOARD_REQUEST_BY_RESIDENCY}?business_unit=${business_unit}`,
        {}
      );
      console.log('Data fetched from residency:', response.data);
      return response.data;
    }
  } catch (error) {
    console.error('Error fetching stats data:', error);
    throw error;
  }
};

export const approveRejectedRequest = async (
  status: string,
  id: number,
  reject_reason: string | null
) => {
  try {
    const requestBody: any = { status };

    if (status !== 'APPROVED') {
      if (!reject_reason) {
        throw new Error('reject_reason is required for this status.');
      }
      requestBody.reject_reason = reject_reason;
    }

    const response = await httpClient.patch(`${APPROVE_REJECTED_REQUEST}/${id}`, requestBody, {});
    return response.data;
  } catch (error) {
    toast.dismiss();
    throw error;
  }
};

export const archiveRequest = async (status: string, id: number) => {
  try {
    const requestBody: any = { status };
    const response = await httpClient.patch(`${ARCHIVE_DSR_REQUEST}/${id}`, requestBody, {});
    return response.data;
  } catch (error) {
    toast.dismiss();
    throw error;
  }
};

export const unarchiveRequest = async (status: string, id: number) => {
  try {
    const requestBody: any = { status };
    const response = await httpClient.patch(`${UNARCHIVE_DSR_REQUEST}/${id}`, requestBody, {});
    return response.data;
  } catch (error) {
    toast.dismiss();
    throw error;
  }
};

export const fetchPerOwner = async (business_unit: number) => {
  try {
    if (business_unit === -1) {
      const response = await httpClient.get(`${REQUESTG_PER_OWNER}`, {});
      return response.data;
    } else {
      const response = await httpClient.get(
        `${REQUESTG_PER_OWNER}?business_unit=${business_unit}`,
        {}
      );
      console.log('Data fetched from residency:', response.data);
      return response.data;
    }
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

//*****************Task Overview******************* */

export const fetchAprovedReqeuestSteps = async (url: string) => {
  try {
    const response = await httpClient.get(url, {});
    return response.data;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};
export const fetchAssigneeData = async (page = 1, size = 10, searchTerm: string = '') => {
  try {
    const response = await httpClient.get(
      `${FETCH_REQUEST_LIST}?page=${page}&size=${size}&search=${encodeURIComponent(searchTerm)}`,
      {
        headers: {
          'x-api-key': api_key,
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data.result;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const fetchTaskListData = async (page = 1, size = 10, searchTerm: string = '') => {
  try {
    const response = await httpClient.get(
      `${ASSIGNED_TASK_LIST}?page=${page}&size=${size}&search=${encodeURIComponent(searchTerm)}`
    );
    return response.data.result;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const createEmailTemplate = async (emailData: { subject: string; content: string }) => {
  console.log('Submitting request data:', emailData);

  try {
    const response = await httpClient.post(`${EMAIL_TEMPLATE}/create`, emailData, {});

    return response.data;
  } catch (error: any) {
    console.error('Failed to create email-template:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'Failed to create email-template.');
  }
};

export const fetchEmailTemplateData = async (customer_id: number | undefined) => {
  try {
    const response = await httpClient.get(`${EMAIL_TEMPLATE}/template/${customer_id}`, {});
    return response.data.result.rows;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed To Fetch Data');
  }
};

export const deleteEmailTemplateData = (templateId: number | string) => {
  const requestData = {
    templateId,
  };

  return fetch(`${DELETE_EMAIL_TEMPLATE}/${templateId}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': api_key,
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(requestData),
  });
};

export const updateEmailTemplateData = async (
  templateId: number | string,
  data: EmailTemplateUpdatePayload
) => {
  if (!data.subject?.trim()) {
    throw new Error('Subject is required');
  }
  if (!data.content?.trim()) {
    throw new Error('Email content is required');
  }

  try {
    const response = await httpClient.patch(`${UPDATE_EMAIL_TEMPLATE}/${templateId}`, data, {});
    return response.data;
  } catch (error) {
    console.error('Error in updating email template:', error);
    throw error;
  }
};

export const fetchMyRequest = async (token: string | null) => {
  const access_token = token;
  try {
    const response = await axios.get(`${GUEST_REQUESTS}`, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${token}`,
      },
    });
    return response;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const fetchMyRequestDetails = async (token: string | null, id: number) => {
  const access_token = token;
  try {
    const response = await axios.get(`${GUEST_REQUESTS}/${id}`, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data.result;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const fetchAllDataGuestRequest = async (token: string | null, id: number) => {
  const access_token = token;
  try {
    const response = await axios.get(`${FETCH_GUEST_REQUEST_ALL_DATA}/${id}`, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${token}`,
      },
    });
    console.log(response.data.result, 'Fetched Data');
    return response.data.result;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const fetchPreviewDataGuest = async (token: string | null, id: number) => {
  const access_token = token;
  try {
    const response = await axios.get(`${FETCH_PREVIEW_REQUEST_DATA_GUEST}/${id}`, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${token}`,
      },
    });
    console.log(response.data.result, 'Fetched Data');
    return response.data.result;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const sendEditorContent = async (token: string | null, data: any) => {
  console.log('testform', data);
  const access_token = token;
  try {
    const response = await axios.post(`${CREATE_REQUEST_FORM}/send-guestside-mail`, data, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error('Failed to add workflow', error);
    return error;
    throw new Error('Failed to add workflow.');
  }
};

export const uploadGuestDocument = async (file: File) => {
  const access_token = token;

  const formData = new FormData();
  formData.append('files', file);
  try {
    const response = await axios.post(UPLOAD_GUEST_REQUESTS, formData, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error('Failed to add workflow', error);
    throw new Error('Failed to save data.');
  }
};

export const fetchAssigneeListById = async (id: number) => {
  try {
    const response = await httpClient.get(`${FETCH_ASSIGNEE_VIEW_DETAILS_LIST}/${id}`, {});
    return response.data.result.tasks;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};

export const SaveAprovedReqeuestSteps = async (url: string, patchData: {}, step: any) => {
  try {
    const response = await httpClient.patch(url, patchData, {});
    return response.data;
  } catch (error) {
    toast.dismiss();
    // toast.error('Failed to update data.');
  }
};

export const uploadIdentificationDocument = async (
  identification_documents: File,
  dsr_data_subject_id: 1,
  dsr_request_id: 1
): Promise<UploadResponse> => {
  const formData = new FormData();
  formData.append('identification_documents', identification_documents);
  formData.append('dsr_data_subject_id', dsr_data_subject_id.toString());
  formData.append('dsr_request_id', dsr_request_id.toString());

  try {
    const response = await httpClient.post(`${UPLOAD_REQUEST_IDENTIFICATION}`, formData, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${token}`,
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Failed to upload document', error);
    throw new Error('Failed to upload document.');
  }
};

export const uploaDLetterOfAuthoraityDocument = async (
  letter_of_authority: File,
  dsr_data_subject_id: 1,
  dsr_request_id: 1
): Promise<UploadResponse> => {
  const formData = new FormData();
  formData.append('letter_of_authority', letter_of_authority);
  formData.append('dsr_data_subject_id', dsr_data_subject_id.toString());
  formData.append('dsr_request_id', dsr_request_id.toString());

  try {
    const response = await httpClient.post(`${UPLOAD_LETTER_OF_AUTHORITY}`, formData, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${token}`,
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Failed to upload document', error);
    throw new Error('Failed to upload document.');
  }
};

export const documentService = {
  uploadDocuments: async (files: File[], url?: string): Promise<UploadResponse> => {
    const formData = new FormData();

    files.forEach((file) => {
      formData.append('files', file);
    });

    if (url) {
      formData.append('url', url);
    }

    try {
      const response = await httpClient.post<UploadResponse>(
        `${UPLOAD_ADD_TASK_ATTACHMENT}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            'x-api-key': api_key,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('Upload failed:', error);
      throw new Error(error.response?.data?.message || 'Upload failed. Please try again.');
    }
  },
};

export const documentServiceRopa = {
  uploadDocuments: async (files: File[], ropa_id?: number): Promise<UploadResponse> => {
    const formData = new FormData();

    files.forEach((file) => {
      formData.append('files', file);
    });

    // if (url) {
    //   formData.append('url', url);
    // }

    try {
      const response = await httpClient.post<UploadResponse>(
        `${UPLOAD_ROPA_AI__ATTACHMENT}${ropa_id}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            'x-api-key': api_key,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('Upload failed:', error);
      throw new Error(error.response?.data?.message || 'Upload failed. Please try again.');
    }
  },
};

export const DeleteDocument = async (url: string) => {
  const requestData = {
    url,
  };

  try {
    const response = await httpClient.delete(`${DELETE_DOCUMENT_DSR}`, {
      data: requestData,
    });
    return response.data;
  } catch (error) {
    console.error('Failed to delete document:', error);
    throw error;
  }
};
// `${FETCH_DASHBOARD_REQUEST_COUNT}?business_unit=${business_unit}`,

export const fetchAssignees = async (group_id: string) => {
  try {
    const user_type = 'active';
    const response = await httpClient.get(
      `${FETCH_ASSIGNEE_DSR_LIST}?user_type=${user_type}&group_id=${group_id}`,
      {}
    );
    return response.data.result;
  } catch (error) {
    console.error('Failed to fetch assignees', error);
    throw new Error('Failed to fetch assignees.');
  }
};
export const uploadDocument = async (file: File) => {
  const formData = new FormData();
  formData.append('files', file);
  try {
    const response = await httpClient.post(UPLOAD_dOCUMENT, formData, {});
    return response.data;
  } catch (error) {
    console.error('Failed to add workflow', error);
    throw new Error('Failed to add workflow.');
  }
};

export const fetchChatActivities = async (id: number) => {
  try {
    const response = await httpClient.get(`${FETCH_MAIL_ACTIVITY_LOG}/${id}`, {});
    return response.data.result;
  } catch (error) {
    console.error('Failed to fetch chat activities', error);
    throw new Error('Failed to fetch chat activities.');
  }
};

export const fetchChatCount = async (id: number) => {
  try {
    const response = await httpClient.patch(`${COUNT_CHAT_ACTIVITY_LOG}/${id}`, {});
    return response.data.result;
  } catch (error) {
    console.error('Failed to fetch chat activities', error);
    throw new Error('Failed to fetch chat activities.');
  }
};
export const fetchLanguageList = async (id: number) => {
  try {
    const response = await httpClient.get(`${FETCH_LANGUAGES_LIST}`, {});
    return response.data.result.data;
  } catch (error) {
    console.error('Failed to fetch chat activities', error);
    throw new Error('Failed to fetch chat activities.');
  }
};

export const deleteDSRFormBuilderLanguage = async (id: number | string) => {
  try {
    const response = await httpClient.delete(`${DELETE_FORM_TRANSLATION}/${id}`, {
      data: { id },
    });
    return response.data;
  } catch (error) {
    console.error('Error deleting DSR form builder:', error);
    throw error;
  }
};

export const getTranslatedLanguage = async (form_id: string, customer_id: number | string) => {
  try {
    const response = await httpClient.get(
      `${FETCH_TRANSLATED_LANGUAGE}/${form_id}/${customer_id}`,
      {}
    );
    return response.data;
  } catch (error) {
    console.error('Failed to fetch control', error);
    throw new Error('Failed to fetch control.');
  }
};

export const getTranslatedContent = async (
  form_id: string,
  customer_id: number | string,
  language_code?: string
) => {
  try {
    const url = language_code
      ? `${FETCH_TRANSLATED_CONTENT}/${form_id}/${customer_id}?language_code=${language_code}`
      : `${FETCH_TRANSLATED_CONTENT}/${form_id}/${customer_id}`;

    const response = await httpClient.get(url, {});
    return response.data;
  } catch (error) {
    console.error('Failed to fetch control', error);
    throw new Error('Failed to fetch control.');
  }
};

export const assignDSRRequest = async (requestId: string | number, assigneeId: number) => {
  try {
    const response = await httpClient.patch(
      `${ASSIGN_DSR_REQUEST}/${requestId}`,
      {
        assignee_id: assigneeId,
      },
      {
        headers: {
          'x-api-key': api_key,
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error assigning DSR request:', error);
    throw error;
  }
};

export const sendMailTemplate = async (data: any) => {
  try {
    const response = await httpClient.post(`${ACTIVITY_MAIL_TEMPLATES}`, data, {});
    return response.data;
  } catch (error) {
    console.error('Failed to send mail template', error);
    throw new Error('Failed to send mail template.');
  }
};

export const resendOtpFormBuilder = async (
  form_id: string,
  request_id: string,
  customer_id: string | number
) => {
  try {
    const response = await httpClient.post(
      `${SEND_OTP_FORM_BUILDER}`,
      {
        form_id: form_id,
        request_id: request_id,
        customer_id: customer_id,
      },
      {
        headers: {
          'x-api-key': api_key,
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Failed to send OTP', error);
    throw new Error('Failed to send OTP.');
  }
};
export const sendOtpGuestLogin = async (email: string) => {
  console.log('Sending OTP to email:', email);

  try {
    const response = await httpClient.post(
      `${SEND_OTP_GUEST_LOGIN}`,
      {
        email,
      },
      {
        headers: {
          'x-api-key': api_key,
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response;
  } catch (error) {
    console.error('Failed to send OTP', error);
    throw new Error('Failed to send OTP.');
  }
};
export const verifyOtpGuestLogin = async (email: string, otp: number) => {
  try {
    const response = await httpClient.post(
      `${VERIFY_OTP_GUEST_LOGIN}`,
      { email, otp },
      {
        headers: {
          'x-api-key': api_key,
          Authorization: `Bearer ${token}`,
        },
      }
    );
    if (response.data?.success) {
      return response.data;
    } else {
      throw new Error(response.data?.message || 'OTP verification failed');
    }
  } catch (error) {
    console.error('Failed to verify OTP', error);
    throw error;
  }
};

export const updateAssigneeTask = async (data: any) => {
  if (data?.documents?.length == 0) delete data?.documents;
  console.log('testform', data);

  const URL = `${ADD_TASK_TASKOVERVIEW}/${data.id}`;
  const formData = { ...data };
  if (formData.id != undefined) delete formData?.id;
  try {
    const response = await httpClient.patch(URL, formData, {});
    return response.data;
  } catch (error) {
    console.error('Failed to add task', error);
    return error;
    throw new Error('Failed to add task.');
  }
};

export const DeleteWorkflowStep = async (id: number | string, step_title: string) => {
  const response = await httpClient.delete(`${COMMON_DELETE_WORKFLOW_STEP}/${id}`, {
    data: { step_title },
  });
  return response;
};

export const editWorkflowStep = async (id: number | string, step_title: string) => {
  try {
    const response = await httpClient.patch(
      `${DELETE_WORKFLOW_STEP}/${id}`,
      {
        step_title,
      },
      {
        headers: {
          'x-api-key': api_key,
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response;
  } catch (error) {
    console.error('Failed to edit workflow', error);
    throw new Error('Failed to edit workflow.');
  }
};
export const moveUpWorkflowStep = async (id: number | string, step_title: string) => {
  try {
    const response = await httpClient.patch(
      `${COMMON_MOVEUP_WORKFLOW_STEP}/${id}`,
      {
        step_title,
      },
      {
        headers: {
          'x-api-key': api_key,
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response;
  } catch (error) {
    console.error('Failed to edit workflow', error);
    throw new Error('Failed to edit workflow.');
  }
};

export const moveDownWorkflowStep = async (id: number | string, step_title: string) => {
  try {
    const response = await httpClient.patch(
      `${COMMON_MOVEDOWN_WORKFLOW_STEP}/${id}`,
      {
        step_title,
      },
      {
        headers: {
          'x-api-key': api_key,
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response;
  } catch (error) {
    console.error('Failed to edit workflow', error);
    throw new Error('Failed to edit workflow.');
  }
};

export const downloadAuditLog = async (request_id: number | undefined) => {
  try {
    const response = await httpClient.get(`${DOWNLOAD_AUDIT_LOG}/${request_id}`, {
      responseType: 'blob',
    });
    return response;
  } catch (error) {
    console.error('Unexpected Error:', error);
    throw error;
  }
};
//Automation list
export const getAutomationList = async (url: string) => {
  try {
    const response = await httpClient.get(
      `${import.meta.env.VITE_APP_GO_TRUST_BASE_API}${url}`,
      {}
    );
    return response;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};
//Automation list
export const setAutomationById = async (url: string, pathData: any) => {
  try {
    const response = await httpClient.patch(url, pathData, {});
    return response;
  } catch (error) {
    toast.dismiss();
    toast.error('Failed to fetch data.');
  }
};
export const updateSelectedWorkflowAutomation = async (
  id: string,
  data: UpdateWorkflowAutomationPayload
) => {
  try {
    const response = await httpClient.patch(
      `${COMMON_SELECTED_WORKFLOW_AUTOMATION}/${id}`,
      {
        activepieces_automation_id: data?.activepieces_automation_id,
        step_title: data?.step_title,
      },
      {
        headers: {
          'x-api-key': api_key,
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Failed to send OTP', error);
    throw new Error('Failed to send OTP.');
  }
};
export const uploadAllDocument = async (file: File) => {
  const formData = new FormData();
  formData.append('files', file);
  try {
    const response = await httpClient.post(UPLOAD_dOCUMENT, formData, {});
    return response.data;
  } catch (error) {
    console.error('Failed to add workflow', error);
    throw new Error('Failed to add workflow.');
  }
};

//Form Builder

export const createFormBuilder = async (formData: {
  name: string;
  busi_unit_id: string;
  regulation_id: [];
  authentication_type: string;
}) => {
  console.log('Submitting request data:', formData);

  try {
    const response = await httpClient.post(`${CREATE_DSR_FORM_BUILDER}`, formData, {});

    return response.data;
  } catch (error: any) {
    console.error('Failed to create request:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'Failed to create request.');
  }
};

export const fetchFormBuilder = async () => {
  try {
    const response = await httpClient.get(`${FETCH_FORM_BUILDER_LIST}`, {});
    return response.data.result.rows;
  } catch (error) {
    console.error('Failed to fetch list', error);
    throw new Error('Failed to fetch list.');
  }
};

export const fetchFormBuilderCategory = async (id: string) => {
  try {
    const response = await httpClient.get(`${FETCH_DSR_FORM_BUILDER_CATEGORY}/${id}`, {});
    return response.data;
  } catch (error) {
    console.error('Failed to fetch category', error);
    throw new Error('Failed to fetch category.');
  }
};

export const fetchBasicInfoControl = async (id: string) => {
  try {
    const response = await httpClient.get(`${FETCH_BASIC_INFO_CONTROL}/${id}`, {});
    return response.data;
  } catch (error) {
    console.error('Failed to fetch control', error);
    throw new Error('Failed to fetch control.');
  }
};

export const createFormBuilderCategory = async (formId: string, formData: AddCategoryFormData) => {
  try {
    const response = await httpClient.post(
      `${CREATE_CATEGORY_DSR_FORM_BUILDER}/${formId}`,
      formData,
      {}
    );

    return response.data;
  } catch (error: any) {
    console.error('Failed to create category:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'Failed to create category');
  }
};

export const updateQuestionOrder = async (
  formId: number | string,
  payload: { orders: { id: number; order: number }[] }
) => {
  try {
    const response = await httpClient.patch(`${UPDATE_FORM_BUILDER_ORDER}/${formId}`, payload, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${token}`,
      },
    });
    return response;
  } catch (error) {
    console.error('Failed to update question order', error);
    throw new Error('Failed to update question order.');
  }
};

export const updateDSRControls = async (id: number | string, payload: any) => {
  try {
    const response = await httpClient.put(`${UPDATE_DSR_CONTROLS}/${id}`, payload, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${token}`,
      },
    });
    return response;
  } catch (error) {
    console.error('Failed to update question', error);
    throw new Error('Failed to update question.');
  }
};

export const createFormVersion = async (formId: string, content: any = {}) => {
  try {
    const response = await httpClient.patch(
      `${UPDATE_FORM_BUILDER_CONTENT}/${formId}`,
      {
        content: content,
      },
      {}
    );
    return response.data;
  } catch (error: any) {
    console.error('Failed to create form version:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'Failed to create form version');
  }
};

export const createQuestion = async (formData: any) => {
  try {
    const response = await httpClient.post(`${ADD_DSR_CONTROL}`, formData, {});

    return response.data;
  } catch (error: any) {
    console.error('Failed to create controls:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'Failed to create controls');
  }
};

export const deleteDSRFormBuilder = async (id: number | string) => {
  try {
    const response = await httpClient.delete(`${DELETE_DSR_FORM_BUILDER}/${id}`, {
      data: { id },
    });
    return response.data;
  } catch (error) {
    console.error('Error deleting DSR form builder:', error);
    throw error;
  }
};

export const deleteDSRFormBuilderQuestion = async (id: number | string) => {
  try {
    const response = await httpClient.delete(`${DELETE_DSR_FORM_BUILDER_QUESTION}/${id}`, {
      data: { id },
    });
    return response.data;
  } catch (error) {
    console.error('Error deleting DSR form builder question:', error);
    throw error;
  }
};

export const publishDsrForm = async (formId: string, customerId: number, url: string) => {
  try {
    const response = await httpClient.patch(`${PUBLISH_DSR_FORM}/${formId}`, { url });
    return response.data;
  } catch (error) {
    console.error('Error publishing form:', error);
    throw error;
  }
};

export const getDsrFormData = async (customer_id: number | string, form_id: string) => {
  try {
    const response = await httpClient.get(
      `${GET_DSR_FORM_BUILDER_DATA}/${customer_id}/${form_id}`,
      {}
    );
    return response.data;
  } catch (error) {
    console.error('Failed to fetch control', error);
    throw new Error('Failed to fetch control.');
  }
};

export const fetchCustomEndpointData = async (
  endpoint: string,
  customer_id: number | string,
  group_id: string
) => {
  try {
    const url = `${COMMON_ENDPOINT}/${endpoint}?customer_id=${customer_id}&group_id=${group_id}`;
    const response = await httpClient.get(url, {});

    return {
      success: true,
      result: response.data.result?.rows || response.data.result,
    };
  } catch (error) {
    console.error(`Error fetching from ${endpoint}:`, error);
    return { success: false, result: [] };
  }
};

export const uploadPublicDocument = async (endpoint: string, file: File) => {
  const access_token = token;
  const formData = new FormData();
  formData.append('files', file);

  try {
    const response = await axios.post(`${COMMON_ENDPOINT}/${endpoint}`, formData, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${access_token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error('Failed to upload document', error);
    throw new Error('Failed to upload document.');
  }
};

export const submitFormBuilder = async (formData: any) => {
  const access_token = Cookies.get('access_token');

  try {
    const response = await httpClient.post(`${SUBMIT_FORM_BUILDER}`, formData, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${access_token}`,
      },
    });

    return response.data;
  } catch (error: any) {
    console.error('Failed to create form:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'Failed to create form');
  }
};

export const verifyOtpFirstTime = async ({
  otp,
  request_id,
  form_id,
  customer_id,
}: {
  otp: number;
  request_id: string;
  form_id: string;
  customer_id: string | number;
}) => {
  const access_token = Cookies.get('access_token');

  try {
    const response = await httpClient.post(
      `${VERIFY_OTP_FORM}/${request_id}/${form_id}`,
      { otp },
      {
        headers: {
          'x-api-key': api_key,
          Authorization: `Bearer ${access_token}`,
        },
      }
    );
    if (response.data?.success) {
      return response.data;
    } else {
      throw new Error(response.data?.message || 'OTP verification failed');
    }
  } catch (error) {
    console.error('Failed to verify OTP', error);
    throw error;
  }
};

export const submitDSRFormContent = async (formData: any): Promise<any> => {
  const access_token = Cookies.get('access_token');
  const formId = formData?.formId;

  try {
    const endpoint = `${UPDATE_FORM_BUILDER_CONTENT}/${formId}`;

    // Restructure the payload to match the expected format
    const payload = {
      content: {
        ...formData.content,
      },
    };

    const response = await httpClient.patch(endpoint, payload, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${access_token}`,
      },
    });
    return response;
  } catch (error) {
    console.error('Error submitting form content:', error);
    throw error;
  }
};
export const checkStatus = async (request_id: string, form_id: string) => {
  const access_token = Cookies.get('access_token');
  try {
    const response = await httpClient.patch(`${CHECK_STATUS}/${request_id}/${form_id}`, {
      headers: {
        'x-api-key': api_key,
        Authorization: `Bearer ${access_token}`,
      },
    });

    return response.data;
  } catch (error) {
    console.error('Error checking DSR status:', error);
    throw error;
  }
};
