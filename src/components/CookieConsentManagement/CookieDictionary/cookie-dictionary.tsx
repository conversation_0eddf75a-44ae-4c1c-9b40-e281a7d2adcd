import { useQuery } from '@tanstack/react-query';
import { ColumnDef } from '@tanstack/react-table';
import { t } from 'i18next';
import { ArrowUpDown } from 'lucide-react';
import { useState } from 'react';
import { useSelector } from 'react-redux';
import { Button } from '../../../@/components/ui/Common/Elements/Button/Button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../@/components/ui/Common/Elements/Select/Select';
import { Switch } from '../../../@/components/ui/switch';
import httpClient from '../../../api/httpClient';
import { RootState } from '../../../redux/store';
import { DomainListProperties, ICookieDetails } from '../../../types/cookie-consent-management';
import { GET_COOKIE_DICTIONARY, ucm_gotrust_basi_api } from '../../common/api';
import { convertDateToHumanView } from '../../common/CommonHelperFunctions';
import DynamicTable from '../../common/ShadcnDynamicTable/dynamic-table';
import { EditCookieModal } from './edit-cookie-modal';

const toHumanReadable = (value: string): string => {
  return value
    .replaceAll('_', ' ') // Replace underscores with spaces
    .replaceAll(/\b\w/g, (char) => char.toUpperCase()); // Capitalize first letter of each word
};
export const CookieDictionary = () => {
  const loginData = useSelector((state: RootState) => state.auth.login.login_details);
  const customer_id = loginData?.customer_id || 0;
  const [totalCount, setTotalCount] = useState(0);
  const [reloadCookieData, setReloadCookieData] = useState(false);
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(10);
  const [selectedDomainId, setSelectedDomainId] = useState<number>(0);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newSize: number) => {
    setSize(newSize);
    setPage(1);
  };
  const { data: domainList } = useQuery({
    queryKey: ['domainList'],
    queryFn: async () => {
      const response = await httpClient.get(
        `${ucm_gotrust_basi_api}/dashboard/display-domains-list-per-customer?customer_id=${customer_id}`
      );
      return response?.data?.result?.rows;
    },
  });

  const {
    data: cookieTableData,
    isLoading,
    refetch,
  } = useQuery<ICookieDetails[]>({
    queryKey: ['GET_COOKIE_DICTIONARY', reloadCookieData, page, size, selectedDomainId],
    queryFn: async () => {
      try {
        let domainQuery = '';
        if (selectedDomainId !== 0) domainQuery = `&domain_id=${selectedDomainId}`;
        const response = await httpClient.get(
          `${GET_COOKIE_DICTIONARY}${customer_id}&page=${page}&page_size=${size}${domainQuery}`
        );
        setTotalCount(response?.data?.result?.count || 0);
        return response?.data?.result?.data;
      } catch (error) {
        console.log(error);
        setTotalCount(0);
      }
    },
    refetchOnWindowFocus: false,
    initialData: [],
  });
  const generateColumns = (data: ICookieDetails[]): ColumnDef<ICookieDetails>[] => {
    if (data.length === 0) return [];

    const keys = Object.keys(data[0]) as (keyof ICookieDetails)[];

    const filteredKeys = [
      ...keys?.filter(
        (item) =>
          item !== 'cookie_service_id' &&
          item !== 'id' &&
          item !== 'cookie_category_id' &&
          item !== 'customer_id' &&
          item !== 'domain_id'
      ),
      'action',
    ];

    return filteredKeys.map((key) => ({
      accessorKey: key,
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {toHumanReadable(key as string)}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        return key === 'action' ? (
          <EditCookieModal cookie={row.original} refetch={refetch} />
        ) : key === 'is_necessary_cookie' ||
          key === 'status' ||
          key === 'show_status' ||
          key === 'Secure' ||
          key === 'HttpOnly' ? (
          <Switch id={key} checked={row.getValue(key)} />
        ) : key === 'created_at' ||
          key === 'updated_at' ||
          key === 'show_status_changed_at' ||
          key === 'status_changed_at' ? (
          <div className="">
            {row.getValue(key) ? convertDateToHumanView(row.getValue(key)) : '-'}
          </div>
        ) : (
          <div className="">{row.getValue(key) ? row.getValue(key) : '-'}</div>
        );
      },
    }));
  };
  const columns: ColumnDef<ICookieDetails>[] = generateColumns(cookieTableData);

  return (
    <div className="flex flex-col pb-10">
      <div className="mb-4 flex h-auto w-full content-end justify-end">
        <div className="w-[250px]">
          <Select
            value={selectedDomainId?.toString()}
            onValueChange={(value) => setSelectedDomainId(Number(value))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0">{t('Cookies.All Domains')}</SelectItem>
              {domainList?.map((domain: DomainListProperties) => {
                return (
                  <SelectItem value={String(domain?.id)} key={domain?.id}>
                    {domain?.domain_name}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>
      </div>
      <DynamicTable<ICookieDetails>
        data={cookieTableData}
        loading={isLoading}
        columns={columns}
        enableSorting
        ServerSidePaginationDetails={{
          totalRecords: totalCount,
          currentPage: page,
          currentSize: size,
          handlePageSizeChange: handlePageSizeChange,
          handlePageNumberChange: handlePageChange,
        }}
      />
    </div>
  );
};
