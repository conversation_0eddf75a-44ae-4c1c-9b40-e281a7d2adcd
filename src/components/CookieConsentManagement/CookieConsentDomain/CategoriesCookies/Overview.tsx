import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { ChartConfig } from '../../../../@/components/ui/chart';
import { RootState } from '../../../../redux/store';
import DonutWithText from '../../../common/Charts/donut-with-text';
import { generateDonutChartColors } from '../../../common/CommonHelperFunctions';
import { get_autoscan_data } from '../../../common/services/cookie-consent-management';

interface CookieeData {
  tableData: any;
}
interface ShadcnChartData {
  key: string | number;
  value: number;
  fill: string;
}
interface ShadcnDonutChartData {
  config: ChartConfig;
  data: ShadcnChartData[];
}
const Overview = ({ tableData }: CookieeData) => {
  const domain_id: number = useSelector(
    (state: RootState) =>
      state?.cookieConsentManagement?.CookieConsentDomain?.cookieConfiguration?.domain_id
  );

  const [data, setData] = useState<{
    domain_url: string;
    domain_id: number;
    auto_scan: boolean;
    scan_frequency: string;
    domain_registration_step: string;
  }>({
    domain_url: '',
    domain_id: domain_id,
    auto_scan: false,
    scan_frequency: '',
    domain_registration_step: '2',
  });

  useEffect(() => {
    const fetchAutoScan = async () => {
      try {
        const responseData = await get_autoscan_data(domain_id);
        const a = responseData.result.data[0];
        const { hostname } = new URL(a.domain_url);

        const d = {
          domain_url: hostname,
          domain_id: a.domain_id,
          auto_scan: a.auto_scan,
          scan_frequency: a.scan_frequency,
          domain_registration_step: '2',
        };
        setData(d);
      } catch (error) {
        console.log(error);
      }
    };
    fetchAutoScan();
  }, []);

  function getUniqueCookieCategories(cookies: any) {
    const uniqueCategories = new Set();
    cookies.forEach((cookie: any) => {
      if (cookie.category) {
        uniqueCategories.add(cookie.category);
      }
    });

    return Array.from(uniqueCategories);
  }
  const uniqueCategories = getUniqueCookieCategories(tableData);

  const colors = generateDonutChartColors(uniqueCategories.length);

  const cookieCategoryDataVales: ShadcnChartData[] = uniqueCategories?.map(
    (item: any, index: number) => {
      const catogoryCount = tableData?.filter((cookie: any) => cookie.category === item).length;
      return {
        key: item,
        value: catogoryCount,
        fill: colors[index],
      };
    }
  );

  const cookieCategoryData = {
    config: {},
    data: cookieCategoryDataVales ?? [],
  };

  const cookieeCount: number = tableData?.length;
  const thirdPartyCookies: number = tableData?.filter(
    (item: any) => item.cookie_type === 'third-party'
  ).length;
  const firstPartyCookies = cookieeCount - thirdPartyCookies;
  const cookiePartyColors = generateDonutChartColors(2);
  const cookieChartdata = {
    config: {
      low: {
        label: 'First Party',
        color: 'hsl(var(--chart-green))',
      },
      medium: {
        label: 'Second Party',
        color: 'hsl(var(--chart-yellow))',
      },
    },
    data: [
      {
        key: 'First Party',
        value: firstPartyCookies,
        fill: cookiePartyColors[0],
      },
      {
        key: 'Second Party',
        value: thirdPartyCookies,
        fill: cookiePartyColors[1],
      },
    ],
  };

  const nonSecureCookies = tableData?.filter((item: any) => item.secure === false).length;
  const secureCookies = cookieeCount - nonSecureCookies;
  const secureCookiesData = {
    config: {},
    data: [
      {
        key: 'Secure',
        value: secureCookies,
        fill: cookiePartyColors[0],
      },
      {
        key: 'Non-Secure',
        value: nonSecureCookies,
        fill: cookiePartyColors[1],
      },
    ],
  };

  return (
    <div className="h-full w-full">
      <div className="mb-4 w-[100%] rounded-lg px-10 py-6 shadow-lg">
        <div className="mb-4">
          <h1 className="text-xl font-medium">Cookies By Category</h1>
          <div>
            <div className="flex h-auto w-full flex-col gap-5">
              {cookieCategoryData?.data?.length === 0 ? (
                <></>
              ) : (
                <>
                  <div className="h-[250px] w-full">
                    <DonutWithText
                      chartConfig={cookieCategoryData?.config}
                      chartData={cookieCategoryData?.data}
                      chartSubtext="Total Cookies"
                      totalValue={cookieeCount}
                    />
                  </div>

                  <div className="flex flex-wrap justify-evenly gap-3">
                    {uniqueCategories?.map((item: any, index: number) => {
                      return (
                        <div
                          key={index}
                          className="flex max-w-full items-center gap-1.5 whitespace-nowrap text-sm"
                        >
                          <div
                            className="size-2 shrink-0 rounded-[2px]"
                            style={{ backgroundColor: colors[index] }}
                          />
                          <span className="max-w-[120px] truncate">{item}</span>
                        </div>
                      );
                    })}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="mb-6 flex flex-col gap-6 md:h-[450px] md:flex-row md:items-center md:justify-center md:gap-6">
        <div className="flex flex-col gap-4 md:h-full md:w-1/3">
          <div className="flex flex-col items-center justify-center rounded-lg px-8 py-6 shadow-lg md:h-[60%]">
            <h1 className="text-center text-xl font-medium">Non-Secure Cookies</h1>
            <p className="mt-6 text-4xl">{nonSecureCookies}</p>
          </div>

          <div className="flex flex-col items-center justify-center gap-4 rounded-lg px-8 py-6 shadow-lg md:h-[40%]">
            <h1 className="text-center text-xl font-medium">Best Practice</h1>
            <p className="text-2xl">0</p>
          </div>
        </div>

        <div className="rounded-lg px-4 py-5 shadow-lg sm:px-6 sm:py-6 md:h-full md:flex-1">
          <p className="text-center text-sm sm:text-base md:text-[1rem]">
            Cookies that are non-secure can be transferred via an un-encrypted connection. A
            man-in-the-middle attack (MiTM) can be used to get the contents of these cookies, hijack
            a session and steal authentication details or sensitive data. You’ll want to minimize
            all non-secure cookies on your web sites.
          </p>

          <div className="mt-4 flex items-center justify-center sm:mt-6">
            <div className="w-full sm:max-w-[400px] md:max-w-[300px]">
              <DonutWithText
                chartConfig={secureCookiesData?.config}
                chartData={secureCookiesData?.data}
                chartSubtext="Total Cookies"
                totalValue={cookieeCount}
              />
            </div>
            <div>
              <div className="flex max-w-full items-center gap-1.5 whitespace-nowrap text-sm">
                <div
                  className="size-2 shrink-0 rounded-[2px]"
                  style={{ backgroundColor: colors[0] }}
                />
                <span className="max-w-[120px] truncate">Secure</span>
              </div>
              <div className="flex max-w-full items-center gap-1.5 whitespace-nowrap text-sm">
                <div
                  className="size-2 shrink-0 rounded-[2px]"
                  style={{ backgroundColor: colors[1] }}
                />
                <span className="max-w-[120px] truncate">Non-Secure</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mb-6 flex flex-col gap-6 md:h-[400px] md:flex-row md:items-center md:justify-center md:gap-6">
        <div className="flex flex-col gap-6 md:h-full md:w-1/3">
          <div className="flex basis-[100%] flex-col items-center justify-center rounded-lg px-6 py-4 shadow-lg">
            <h1 className="text-center text-xl font-medium">Unique Cookies</h1>
            <p className="mt-6 text-4xl">{cookieeCount}</p>
          </div>
        </div>

        <div className="rounded-lg px-6 py-6 shadow-lg md:h-full md:flex-1">
          <p className="text-center text-sm md:text-base">
            Cookies are vital to your customers' experience on your website. Most companies will
            want to keep a precise list of all cookies found on their web properties. We can help
            with that.
          </p>
          <div className="mt-4 flex items-center justify-center sm:mt-6">
            <div className="w-full sm:max-w-[400px] md:max-w-[300px]">
              <DonutWithText
                chartConfig={cookieChartdata?.config}
                chartData={cookieChartdata?.data}
                chartSubtext="Total Cookies"
                totalValue={cookieeCount}
              />
            </div>
            <div>
              <div className="flex max-w-full items-center gap-1.5 whitespace-nowrap text-sm">
                <div
                  className="size-2 shrink-0 rounded-[2px]"
                  style={{ backgroundColor: colors[0] }}
                />
                <span className="max-w-[120px] truncate">First Party</span>
              </div>
              <div className="flex max-w-full items-center gap-1.5 whitespace-nowrap text-sm">
                <div
                  className="size-2 shrink-0 rounded-[2px]"
                  style={{ backgroundColor: colors[1] }}
                />
                <span className="max-w-[120px] truncate">Third Party</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mb-6 flex h-[200px] items-center justify-center gap-3">
        <div className="flex h-full w-1/3 flex-none flex-col gap-1">
          <div className="flex h-full flex-col items-center justify-center rounded-lg px-10 py-6 shadow-lg">
            <div>
              <h1 className="text-xl font-medium">Pages Scanned</h1>
            </div>
            <div>
              <p className="mt-6 text-4xl">100</p>
            </div>
          </div>
        </div>
        <div className="gap- flex h-full grow flex-col rounded-lg px-12 py-8 shadow-lg">
          <div className="text-md mb-4 text-center font-medium">
            DOMAIN AUDITED :{' '}
            <span className="font-semibold italic">{data?.domain_url ? data?.domain_url : ''}</span>
          </div>
          <div className="text-center">
            To govern at scale we recommend auditing the most important pages on your website at a
            regular cadence even as frequently as daily. A full scan of your entire index should be
            performed at least quarterly.
          </div>
        </div>
      </div>
    </div>
  );
};

export default Overview;
