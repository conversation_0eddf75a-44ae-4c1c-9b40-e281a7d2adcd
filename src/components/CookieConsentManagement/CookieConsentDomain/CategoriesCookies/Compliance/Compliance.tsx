import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, CirclePlus, Shield, SquarePen } from 'lucide-react';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import z from 'zod';
import { Badge } from '../../../../../@/components/ui/badge';
import { Button } from '../../../../../@/components/ui/Common/Elements/Button/Button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../../../@/components/ui/Common/Elements/Card/Card';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../../../../../@/components/ui/Common/Elements/Dialog/Dialog';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../../../../../@/components/ui/Common/Elements/Popover/Popover';
import { Textarea } from '../../../../../@/components/ui/textarea';
import httpClient from '../../../../../api/httpClientNew';
import { useActivityHook } from '../../../../../hooks/Privacy-ops/useActivityHook';
import { RootState } from '../../../../../redux/store';
import { cleanAndFormatText } from '../../../../../utils/helperData';
import { ADD_ACTION } from '../../../../common/api';
import {
  create_observation,
  get_cookie_risk_score,
  get_cookies_cookiesetup,
} from '../../../../common/services/cookie-consent-management';
import { addNewDuty } from '../../../../common/services/privacy-ops-duty';
import DynamicTable from '../../../../common/ShadcnDynamicTable/dynamic-table';
import { SubmittedDataProperties } from '../../../../PrivacyOps/Activities/Actions/ActionActivity';
import { AddAction } from '../../../../PrivacyOps/Activities/Actions/AddAction';
import { AddActivitySchema } from '../../../../PrivacyOps/Activities/Actions/AddActivitySchema';
import { AddDuty } from '../../../../PrivacyOps/Activities/Duty/AddDuty';
import { AddDutySchema } from '../../../../PrivacyOps/Activities/Duty/DutyAcitivtySchema';
import CompliancePieChart from './CompliancePieChart';
import ComplianceRecommendations from './ComplianceRecommendations';
import ComplianceSummary from './ComplianceSummary';
import RiskMatrix from './RiskMatrix';
// import your CookieRiskTable if needed

interface ResponseDataProperties {
  id: number;
  category: string;
  cookie_key: string;
  path: string;
  cookie_type: string;
  description: string;
  vendor_privacy_policy_link: string;
  secure: boolean;
  status_changed_at: string;
  category_id: number;
  show_status_changed_at: string;
  created_at: string;
  updated_at: string;
  httpOnly: boolean;
  status?: boolean;
  show_status?: boolean;
  is_necessary_cookie?: boolean;
  //   customer_id: number;
  cookie_service_id: number;
  action?: string;
  domain_id: number;
  observation: string;
}

const complianceMetrics = [
  { framework: 'GDPR', score: 94, status: 'Compliant' },
  { framework: 'CCPA', score: 89, status: 'Compliant' },
  { framework: 'LGPD', score: 86, status: 'Review Needed' },
  { framework: 'PIPEDA', score: 92, status: 'Compliant' },
];

interface ComplianceTabProps {
  cookieData: any; // Replace 'any' with the actual type if known
}

const ComplianceTab: React.FC<ComplianceTabProps> = ({ cookieData }) => {
  console.log({ cookieData });
  const [openTextModal, setOpentextModal] = useState<boolean>(false);
  const [auditObservations, setAuditObservations] = useState<Record<string, string>>({});
  const [domainCookieId, setDomainCookieId] = useState<number>();
  const [cookieTableData, setCookieTableData] = useState(cookieData);
  const [loading, setLoading] = useState<boolean>(false);
  const [observationValue, setObservationValue] = useState<string>();
  const [domainId, setDomainId] = useState<number>();
  const [refetchData, setRefetchData] = useState<boolean>();
  const [addDuty, setAddDuty] = useState<boolean>(false);
  const [addAction, setAddAction] = useState<boolean>(false);
  const { t } = useTranslation();
  const domain_id: number = useSelector(
    (state: RootState) =>
      state?.cookieConsentManagement?.CookieConsentDomain?.cookieConfiguration?.domain_id
  );

  const {
    getDuty,
    isDutyLoading,
    dutyData,
    getAction,
    getArchieveDuty,
    archieveData,
    isArchieveDataLoading,
    settingCurrentTab,
    dutyCount,
    getDutiesCount,
    updateSearchTerm,
    settingShowDialog,
    showDialog,
    userData,
    entities,
    selectedEntityId,
    getActionsCount,
    setSelectedEntityId,
  } = useActivityHook();

  const handleObservationSubmit = async () => {
    try {
      const response = await create_observation(domainId, domainCookieId, observationValue);

      if (response?.status_code === 200) {
        console.log(response?.result?.data[0]?.observation, 'yfyefd');
        setObservationValue(response?.result?.data[0]?.observation);
        setRefetchData(!refetchData);
        toast.dismiss();
        toast.success('Observation added successfully');
      }
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(() => {
    const fetchCookieCategories = async () => {
      setLoading(true);
      try {
        const c_data = await get_cookies_cookiesetup(domain_id);
        const cookie_data = c_data?.result?.data.map((a: any) => ({
          cookie_key: a?.cookie_key,
          id: a?.id,
          path: a?.path,
          service: a?.cookie_service_name,
          category: a?.cookie_category_name,
          expiration: a?.expiration,
          cookie_type: a?.cookie_type,
          vendor_name: a?.vendor_name,
          subpage_name: a?.subpage_name !== 'N/A' ? a?.subpage_name : '-',
          description: a?.description,
          // status_changed_at: a?.status_changed_at,
          // show_status_changed_at: a?.show_status_changed_at,
          vendor_privacy_policy_link: a?.vendor_privacy_policy_link,
          secure: a?.Secure,
          // status: a?.status,
          httpOnly: a?.HttpOnly,
          // show_status: a?.show_status,
          category_id: a?.cookie_category_id,
          cookie_service_id: a?.cookie_service_id,
          is_necessary_cookie: a?.is_necessary_cookie,
          action: '',
          observation: a?.observation,
          domain_id: a?.domain_id,
        }));

        // After fetching cookie data, also fetch and apply risk scores
        const riskResponse = await get_cookie_risk_score(domain_id);
        const updatedCookieDataWithRisk = cookie_data.map((cookie: any) => {
          const riskScore = riskResponse?.result?.data.find((item: any) => item.id === cookie.id);
          return {
            ...cookie,
            risk_score: riskScore?.risk_score ?? '-',
            risk_level: riskScore?.risk_level ?? '-',
            risk_tags: riskScore?.risk_tags ?? [],
          };
        });

        setCookieTableData(updatedCookieDataWithRisk);
        setLoading(false);
      } catch (error) {
        console.error(error);
      }
    };
    fetchCookieCategories();
  }, [refetchData, domain_id]);

  const handleDutySubmit = async (submittedData: z.infer<typeof AddDutySchema>) => {
    const finalData = {
      title: submittedData?.title,
      assignee_id: submittedData?.assignee_id,
      comment: submittedData?.comment,
      standard: submittedData?.standard,
      due_date: submittedData?.due_date,
      entity_id: Number(submittedData?.entity_id),
    };
    const response = await addNewDuty(finalData);
    if (response?.status_code === 200) {
      getDuty();
      getDutiesCount();
      setAddDuty(false);
    }
  };

  const handleActionSubmit = async (submittedData: z.infer<typeof AddActivitySchema>) => {
    // const { t } = useTranslation();
    const finalData: SubmittedDataProperties = {
      description: submittedData?.description,
      assigned_by: Number(submittedData?.assigned_by),
      assigned_to: Number(submittedData?.assigned_to),
      title: submittedData?.title,
      assigned_date: submittedData?.assigned_date,
      deadline_date: submittedData?.deadline_date,
      regulation_id: Number(submittedData?.regulation_id),
      entity_id: Number(submittedData?.entity_id),
      custom_busi_requirement_id: Number(submittedData?.custom_busi_requirement_id),
    };
    const response = await httpClient.post(ADD_ACTION, finalData);
    if (response?.status === 200) {
      toast.dismiss();
      toast.success(t('DPO.Activities.Action.ActionActivity.ActionAddedSuccessfully'));
      getAction();
      setAddAction(false);
      getActionsCount();
    }
  };

  const columns: ColumnDef<ResponseDataProperties>[] = [
    {
      accessorKey: 'cookie_key',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Cookie key
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('cookie_key')}</div>,
    },
    {
      accessorKey: 'category',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Category
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div className="">{row.getValue('category')}</div>,
    },
    {
      accessorKey: 'risk_score',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Risk Score
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const riskLevel: string | null = row.getValue('risk_level');

        let riskBgcolor = '';
        if (riskLevel) {
          if (riskLevel === 'Low') {
            riskBgcolor = 'bg-[#2DB079]';
          } else if (riskLevel === 'Medium') {
            riskBgcolor = 'bg-[#FD9E08]';
          } else if (riskLevel === 'High') {
            riskBgcolor = 'bg-[#FF0505]';
          }
        } else {
          riskBgcolor = 'bg-[#EED202]';
        }
        return (
          <Button className={`${riskBgcolor} min-w-10 hover:bg-${riskBgcolor} text-white`}>
            {row.getValue('risk_score') ?? '-'}
          </Button>
        );
      },
    },
    {
      accessorKey: 'risk_level',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Risk Level
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const riskLevel: string | null = row.getValue('risk_level');

        let riskBgcolor = '';
        if (riskLevel) {
          if (riskLevel === 'Low') {
            riskBgcolor = 'border-[#2DB079] bg-[#F0FDF4]';
          } else if (riskLevel === 'Medium') {
            riskBgcolor = 'border-[#FD9E08] bg-[#FEFCE8]';
          } else if (riskLevel === 'High') {
            riskBgcolor = 'border-[#FF0505]  bg-[#FEF2F2]';
          }
        } else {
          riskBgcolor = 'bg-[#EED202]';
        }
        return (
          <div
            className={`flex items-center justify-center whitespace-nowrap rounded border px-2 py-1 text-xs font-medium text-primary-text ${riskBgcolor}`}
          >
            {row.getValue('risk_level') ?? '-'}
          </div>
        );
      },
    },
    {
      accessorKey: 'domain_id',
      header: () => null,
      cell: () => null,
    },
    {
      accessorKey: 'id',
      header: () => null,
      cell: () => null,
    },
    {
      accessorKey: 'risk_tags',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Risk Tags
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const riskTags: string[] | null = row.getValue('risk_tags');

        return (
          <div className="flex flex-wrap gap-2 text-center">
            {riskTags && riskTags.length > 0 ? (
              riskTags.map((tag: string, index: number) => (
                <Badge
                  key={index}
                  className="flex items-center justify-center px-2 py-1 text-xs font-medium text-white"
                >
                  {cleanAndFormatText(tag)}
                </Badge>
              ))
            ) : (
              <span className="text-xs text-gray-500">-</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'observation',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Audit Observation
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const domain_Cookie_Id = row.getValue('id') as number;
        const observation = row.getValue('observation') as string;
        const domainId = row.getValue('domain_id') as number;

        return (
          <div>
            {observation ? (
              <div>
                <span className="ml-3">{observation}</span>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button>
                      <SquarePen size={14} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="flex w-auto flex-col px-1 py-2" align="start">
                    <Button
                      onClick={() => {
                        setAddDuty(true);
                      }}
                    >
                      Add duty
                    </Button>
                    <Button onClick={() => setAddAction(true)}>Add action</Button>
                  </PopoverContent>
                </Popover>
              </div>
            ) : (
              <Button
                onClick={() => {
                  setDomainId(domainId);
                  setDomainCookieId(domain_Cookie_Id);
                  setObservationValue(observation || '');
                  setTimeout(() => {
                    setOpentextModal(true);
                  }, 100);
                }}
              >
                <CirclePlus size={14} />
                <span className="ml-2 text-xs">Add Audit Observation</span>
              </Button>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <>
      {addDuty && (
        <AddDuty
          showDialog={addDuty}
          handleSubmit={handleDutySubmit}
          setShowDialog={setAddDuty}
          userData={userData}
          entities={entities}
        />
      )}

      {addAction && (
        <AddAction
          showDialog={addAction}
          userData={userData}
          entities={entities}
          handleSubmit={handleActionSubmit}
          setShowDialog={setAddAction}
        />
      )}
      <Dialog open={openTextModal} onOpenChange={setOpentextModal}>
        <DialogContent className="h-auto overflow-auto sm:max-w-[30%]">
          <DialogHeader>
            <DialogTitle className="text-base font-semibold text-gray-700">
              Add Audit Observation
            </DialogTitle>
          </DialogHeader>
          <div className="flex flex-col gap-2">
            <Textarea
              id="description"
              name="description"
              placeholder="Write"
              value={observationValue || ''}
              onChange={(e) => setObservationValue(e.target.value)}
              className={`border-border} border border-solid`}
            />
          </div>
          <DialogFooter className="flex justify-center">
            <Button
              size="sm"
              variant="secondary"
              type="button"
              onClick={() => {
                setOpentextModal(false);
              }}
            >
              Cancel
            </Button>
            <Button
              size="sm"
              type="button"
              className="bg-custom-primary text-white hover:bg-custom-primary"
              onClick={() => {
                handleObservationSubmit();

                setOpentextModal(false);
              }}
            >
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <div className="space-y-6 p-4">
        <ComplianceSummary cookies={cookieData} />
        <div
          className="table_main_content min-w-[200px] overflow-auto rounded-lg bg-primary-background"
          style={{ height: 'calc(100vh - 7rem)' }}
        >
          <DynamicTable<ResponseDataProperties>
            data={cookieTableData}
            loading={loading}
            columns={columns}
            enableSorting
            enablePagination
          />
        </div>
        <div className="flex flex-col gap-6 md:flex-row">
          <Card className="w-full">
            <CardContent className="flex-1 p-4">
              <h3 className="mb-2 font-semibold">Risk Matrix</h3>
              <RiskMatrix cookies={cookieData} />
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <h3 className="mb-2 font-semibold">Risk Distribution</h3>
              <CompliancePieChart cookies={cookieData} />
            </CardContent>
          </Card>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Compliance Status</CardTitle>
            <CardDescription>Regulatory compliance across different frameworks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {complianceMetrics.map((compliance, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between rounded-lg border p-4"
                >
                  <div className="flex items-center space-x-4">
                    <Shield className="h-8 w-8 text-blue-600" />
                    <div>
                      <h4 className="font-medium">{compliance.framework}</h4>
                      <p className="text-sm text-gray-600">Compliance Score: {compliance.score}%</p>
                    </div>
                  </div>
                  <Badge
                    variant={compliance.status === 'Compliant' ? 'default' : 'secondary'}
                    className={compliance.status === 'Compliant' ? 'text-white' : ''}
                  >
                    {compliance.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="px-4 py-2">
            <ComplianceRecommendations cookies={cookieData} />
          </CardContent>
        </Card>
      </div>
    </>
  );
};
export default ComplianceTab;
