import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Too<PERSON><PERSON> } from 'recharts';
import { calculateScore, getRiskLevel } from '../scoring';

interface Cookie {
  // Define the properties of a cookie object based on what calculateScore expects
  [key: string]: any;
}

interface CompliancePieChartProps {
  cookies: <PERSON><PERSON>[];
}

const CompliancePieChart = ({ cookies }: CompliancePieChartProps) => {
  const riskCounts = { Low: 0, Medium: 0, High: 0 };

  cookies.forEach((cookie) => {
    const score = calculateScore(cookie);
    const risk = getRiskLevel(score);
    riskCounts[risk]++;
  });

  const data = Object.entries(riskCounts).map(([name, value]) => ({ name, value }));

  const COLORS = ['#10b981', '#facc15', '#ef4444'];

  return (
    <PieChart width={300} height={250}>
      <Pie data={data} cx={150} cy={100} innerRadius={50} outerRadius={80} label dataKey="value">
        {data.map((_, i) => (
          <Cell key={i} fill={COLORS[i]} />
        ))}
      </Pie>
      <Tooltip />
      <Legend />
    </PieChart>
  );
};

export default CompliancePieChart;
