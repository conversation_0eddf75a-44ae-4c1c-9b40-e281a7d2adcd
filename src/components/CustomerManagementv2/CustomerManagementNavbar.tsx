import React, { ChangeEvent } from 'react';

import { Separator } from '../../@/components/ui/Common/Elements/Seperator/Seperator';

const navHeadings = ['Customers', 'Audit Log'];

interface CustomerManagementNavbarProperties {
  selectedItem: string;
  handleItemChange: (value: string) => void;
  updateSearchTerm: (event: ChangeEvent<HTMLInputElement>) => void;
}

const CustomerManagementNavbar: React.FC<CustomerManagementNavbarProperties> = ({
  selectedItem,
  handleItemChange,
  updateSearchTerm,
}) => {
  //! VARIABLES

  const underline =
    'underline underline-offset-[9.5px] decoration-custom-primary decoration-4 rounded-xl text-[#3C3C3C]';

  //! HANDLER FUNCTIONS

  return (
    <>
      <nav className="mb-[-3px] font-primary-text">
        <div className="flex w-full flex-row flex-wrap items-center justify-between pb-2">
          <div
            className="flex w-full flex-row flex-wrap justify-between md:flex md:w-auto"
            style={{ width: '100%' }}
            id="navbar-default"
          >
            <ul className="flex flex-row gap-10">
              {navHeadings.map((item) => {
                let cssClass = 'font-[Poppins] text-sm font-medium leading-normal capitalize ';
                cssClass += selectedItem === item ? underline : 'text-[#999]';

                return (
                  <li key={item} className="flex items-end">
                    <button className={cssClass} onClick={() => handleItemChange(item)}>
                      {item}
                    </button>
                  </li>
                );
              })}
            </ul>
          </div>
        </div>
      </nav>
      <Separator className="mb-4 bg-[#C0CDE0]" />
    </>
  );
};

export default CustomerManagementNavbar;
