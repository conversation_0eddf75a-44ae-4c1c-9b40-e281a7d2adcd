import { useQuery } from '@tanstack/react-query';
import { ColumnDef } from '@tanstack/react-table';
import { debounce } from 'lodash';
import { ArrowUpDown, Circle, CirclePlus, Pencil } from 'lucide-react';
import moment from 'moment';
import { useCallback, useState } from 'react';
import { DateRange } from 'react-day-picker';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../@/components/ui/Common/Elements/Button/Button';
import { DatePickerWithRange } from '../../@/components/ui/Common/Elements/Date/DateRangePicker';
import { Input } from '../../@/components/ui/Common/Elements/Input/Input';
import httpClient from '../../api/httpClient';
import search from '../../assets/Search.svg';
import { getInitialsByName } from '../../utils/helperData';
import {
  CUSTOMER_MANAGEMENT_ADD_CUSTOMER,
  CUSTOMER_MANAGEMENT_EDIT_CUSTOMER,
  CUSTOMER_MANAGEMENT_VIEW_CUSTOMER,
} from '../../utils/routeConstant';
import AvatarFrame from '../common/Avatar';
import { convertDateToHumanView } from '../common/CommonHelperFunctions';
import DynamicTable from '../common/ShadcnDynamicTable/dynamic-table';
import { GET_CUSTOMER_MANAGEMENT_LIST } from '../common/api';

export interface CustomerItem {
  id: number;
  name: string;
  Users: Users[];
  email: string;
  status: string;
  address: string;
  createdAt: string;
  updatedAt: string;
}

interface Users {
  firstName: string;
  lastName: string;
}

interface CustomerManagementTableLayoutProps {
  searchValue: string;
}

const CustomerManagementTableLayout: React.FC<CustomerManagementTableLayoutProps> = ({
  searchValue,
}) => {
  const loginData = useSelector((state: any) => state.auth.login.login_details);
  let customer_id = 0;
  if (loginData !== null) {
    customer_id = loginData.customer_id;
  }
  const navigate = useNavigate();
  const [page, setPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [date, setDate] = useState<DateRange | undefined>({
    from: undefined,
    to: undefined,
  });

  const handleSearchInput = useCallback(
    debounce((value: string) => {
      setSearchTerm(value);
      setPage(1);
    }, 1000),
    []
  );

  const handlePageNumberChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setPage(1);
  };

  const { data, isLoading } = useQuery({
    queryKey: ['customers', customer_id, page, pageSize, searchTerm, searchValue, date],
    queryFn: async () => {
      let query = `?page=${page}&size=${pageSize}&customer_id=${customer_id}`;

      if (date?.from && date?.to) {
        const start = moment(date.from).format('YYYY-MM-DD');
        const end = moment(date.to).format('YYYY-MM-DD');
        query += `&start_date=${start}&end_date=${end}`;
      }

      // Use searchValue from navbar if available, otherwise use local searchTerm
      const finalSearchTerm = searchValue || searchTerm;
      if (finalSearchTerm !== '') {
        query += `&search=${finalSearchTerm}`;
      }

      const response = await httpClient.get(`${GET_CUSTOMER_MANAGEMENT_LIST}${query}`);
      setTotalCount(response?.data?.result?.count || 0);
      return response?.data?.result?.rows ?? [];
    },
    initialData: [],
  });

  const filteredData = data.filter((item: any) =>
    Object.values(item).some((value: any) =>
      value
        .toString()
        .toLowerCase()
        .includes((searchValue || searchTerm).toLowerCase())
    )
  );

  const columns: ColumnDef<CustomerItem>[] = [
    {
      accessorKey: 'name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Name
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const fill =
          row?.original?.status === 'active'
            ? '#00CBA0'
            : row?.original?.status === 'inactive'
              ? '#FF9950'
              : '#FA1464';

        return (
          <div className="flex items-center">
            <svg width="11" height="11" viewBox="0 0 11 11" xmlns="http://www.w3.org/2000/svg">
              <circle cx="5.5" cy="5.35156" r="5" fill={fill} />
            </svg>
            <span className="ml-2">{row.getValue('name')}</span>
          </div>
        );
      },
    },

    {
      accessorKey: 'email',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Email
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div>{row.getValue('email')}</div>,
    },
    {
      accessorKey: 'Users',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Contact Person
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const users: Users[] = row.getValue('Users');
        const fullName =
          users && users.length > 0 ? `${users[0].firstName} ${users[0].lastName}` : '';

        return (
          <div className="flex items-center gap-2">
            <AvatarFrame value={fullName} getInitials={getInitialsByName} />
            <span>{fullName}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'address',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Address
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => <div>{row.getValue('address')}</div>,
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Added Date
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const addedDate: string | number | Date = row.getValue('createdAt');
        return <div>{addedDate ? convertDateToHumanView(addedDate.toString()) : '-'}</div>;
      },
    },
    {
      accessorKey: 'updatedAt',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Last Updated
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const updatedDate: string | number | Date = row.getValue('updatedAt');
        return <div>{updatedDate ? convertDateToHumanView(updatedDate.toString()) : '-'}</div>;
      },
    },
    {
      accessorKey: 'action',
      header: 'Action',
      cell: ({ row }) => (
        <Button
          onClick={(event) => {
            event.stopPropagation();
            navigate(`${CUSTOMER_MANAGEMENT_EDIT_CUSTOMER}`, {
              state: {
                id: row.original.id,
              },
            });
          }}
          type="button"
        >
          <Pencil />
        </Button>
      ),
    },
  ];

  const handleRowClick = (rowData: CustomerItem): void => {
    navigate(`${CUSTOMER_MANAGEMENT_VIEW_CUSTOMER}`, {
      state: {
        id: rowData.id,
      },
    });
  };

  return (
    <main className="size-full">
      <nav className="flex h-fit w-full flex-row flex-wrap justify-between gap-2">
        {/* Search and Date */}
        <div className="flex h-full gap-8">
          <Input
            icon={search}
            onChange={(e) => handleSearchInput(e.target.value)}
            className="flex h-full w-[295px] items-center justify-between rounded-md border border-solid border-input bg-transparent font-primary-text text-[13px] font-light text-primary"
            placeholder="Search"
          />

          <DatePickerWithRange
            date={date}
            setDate={setDate}
            className="flex h-full w-[295px] items-center justify-between rounded-md border border-solid border-input bg-transparent font-primary-text text-[13px] font-light text-primary"
          />
        </div>

        <div className="w-fit">
          <Button
            className="gap-2 bg-custom-primary text-white hover:bg-custom-primary hover:text-white"
            onClick={() => navigate(`${CUSTOMER_MANAGEMENT_ADD_CUSTOMER}`, {})}
          >
            <CirclePlus size={20} />
            <p>Add Customer</p>
          </Button>
        </div>
      </nav>

      <div className="my-2 flex flex-row justify-end gap-[31px]">
        <div className="flex flex-row items-center justify-between gap-1">
          <div className="pr-2 text-green-500">
            <Circle className="size-2 fill-current" />
          </div>
          <span className="font-[Poppins] text-[11px] font-medium leading-[13.981px] text-[#262626]">
            Active Customers
          </span>
        </div>

        <div className="flex flex-row items-center justify-between gap-1">
          <div className="pr-2 text-orange-500">
            <Circle className="size-2 fill-current" />
          </div>
          <span className="font-[Poppins] text-[11px] font-medium leading-[13.981px] text-[#262626]">
            Inactive Customers
          </span>
        </div>

        <div className="flex flex-row items-center justify-between gap-1">
          <div className="pr-2 text-red-500">
            <Circle className="size-2 fill-current" />
          </div>
          <span className="font-[Poppins] text-[11px] font-medium leading-[13.981px] text-[#262626]">
            Archived Customers
          </span>
        </div>
      </div>

      <div className="my-5">
        <DynamicTable
          enableSorting
          data={filteredData}
          onRowClick={handleRowClick}
          columns={columns}
          loading={isLoading}
          ServerSidePaginationDetails={{
            totalRecords: totalCount,
            currentPage: page,
            currentSize: pageSize,
            handlePageNumberChange,
            handlePageSizeChange,
          }}
        />
      </div>
    </main>
  );
};

export default CustomerManagementTableLayout;
