import { ChangeEvent, useState } from 'react';
import CustomerManagementAuditLog from './CustomerManagementAuditLog';
import CustomerManagementNavbar from './CustomerManagementNavbar';
import CustomerManagementTableLayout from './CustomerManagementTableLayout';

const CustomerManagementList: React.FC = () => {
  const [selectedNavItem, setSelectedNavItem] = useState('Customers');
  const [searchValue, setSearchValue] = useState<string>('');

  const handleNavItemChange = (value: string) => {
    setSelectedNavItem(value);
  };

  const updateSearchTerm = (event: ChangeEvent<HTMLInputElement>) => {
    setSearchValue(event.target.value);
  };

  return (
    <div className="m-3 bg-white p-5">
      <CustomerManagementNavbar
        selectedItem={selectedNavItem}
        handleItemChange={handleNavItemChange}
        updateSearchTerm={updateSearchTerm}
      />

      {selectedNavItem === 'Customers' && (
        <CustomerManagementTableLayout searchValue={searchValue} />
      )}

      {selectedNavItem === 'Audit Log' && <CustomerManagementAuditLog searchValue={searchValue} />}
    </div>
  );
};

export default CustomerManagementList;
