export const dsrWorkflowTableData = [
  {
    flowtype: 'Data Access',
    created_by: '<PERSON>',
    created_date: '2024-08-25T09:57:33.244Z',
    workflow: 'Published',
  },
  {
    flowtype: 'Data Erasure',
    created_by: '<PERSON>',
    created_date: '2024-08-20T09:57:33.244Z',
    workflow: 'Published',
  },
  {
    flowtype: 'Data Rectification',
    created_by: '<PERSON><PERSON><PERSON>',
    created_date: '2024-08-18T09:57:33.244Z',
    workflow: 'Draft',
  },
];

export const dsrAddWorkflowTableData = [
  {
    title: 'Procurement',
    assigned_to: '<PERSON>',
    created_date: '2024-08-25T09:57:33.244Z',
    end_date: '2024-09-25T09:57:33.244Z',
    department: 'Finance',
    priority: 'Low',
    attachment: 4,
  },
  {
    title: 'Upload Attachements',
    assigned_to: '<PERSON><PERSON><PERSON>',
    created_date: '2024-08-18T09:57:33.244Z',
    end_date: '2024-09-30T09:57:33.244Z',
    department: 'HR',
    priority: 'High',
    attachment: 3,
  },
  {
    title: '<PERSON><PERSON> Acknowledgement email',
    assigned_to: '<PERSON>',
    created_date: '2024-08-10T09:57:33.244Z',
    end_date: '2024-08-25T09:57:33.244Z',
    department: 'Sales',
    priority: 'Medium',
    attachment: 5,
  },
];

export const dsrEditVerifyWorkflowTableData = [
  {
    id: 1,
    activepieces_automation_id: 'sss',
    guidance_text: 'aaa',
    title: 'Data Verification',
    assigned_to: 'Adam Marksman',
    created_date: '2024-09-25T09:57:33.244Z',
    end_date: '2024-10-25T09:57:33.244Z',
    department: 'Marketing',
    priority: 'Medium',
    attachment: 2,
    assignee_id: ['1'],
    due_days: 1,
    documents: [],
    TaskDocuments: [],
    task_type: '1',
  },
  {
    id: 1,
    activepieces_automation_id: 'sss',
    guidance_text: 'aaa',

    title: 'Verify identity of the Subject',
    assigned_to: 'Samuel Reeves',
    created_date: '2024-07-18T09:57:33.244Z',
    end_date: '2024-09-25T09:57:33.244Z',
    department: 'Sales',
    priority: 'High',
    attachment: 7,
    assignee_id: ['1'],
    due_days: 1,
    documents: [],
    TaskDocuments: [],
    task_type: '1',
  },
  {
    id: 1,
    activepieces_automation_id: 'sss',
    guidance_text: 'aaa',
    title: 'Data Gathering',
    assigned_to: 'Tom Smith',
    created_date: '2024-08-10T09:57:33.244Z',
    end_date: '2024-08-25T09:57:33.244Z',
    department: 'SEO',
    priority: 'Medium',
    attachment: 6,
    assignee_id: ['1'],
    due_days: 1,
    documents: [],
    TaskDocuments: [],
    task_type: '1',
  },
];

export const dsrEditAcknowledgementWorkflowTableData = [
  {
    title: 'Sent Acknowledgement email',
    assigned_to: 'Tom Smith',
    created_date: '2024-08-17T09:57:33.244Z',
    end_date: '2024-08-30T09:57:33.244Z',
    department: 'Finance',
    priority: 'Medium',
    attachment: 4,
  },
];

export const dsrEditDataGatheringWorkflowTableData = [
  {
    title: 'Procurement',
    assigned_to: 'Adam Marksman',
    created_date: '2024-09-25T09:57:33.244Z',
    end_date: '2024-10-25T09:57:33.244Z',
    department: 'Marketing',
    priority: 'Medium',
    attachment: 7,
  },
  {
    title: 'Data Verification',
    assigned_to: 'Nishant Singh',
    created_date: '2024-08-17T09:57:33.244Z',
    end_date: '2024-10-30T09:57:33.244Z',
    department: 'Finance',
    priority: 'Low',
    attachment: 3,
  },
];

export const dsrEditDataCleansingWorkflowTableData = [
  {
    title: 'Verify identity of the Subject',
    assigned_to: 'Julie Kingsman',
    created_date: '2024-08-26T09:57:33.244Z',
    end_date: '2024-11-25T09:57:33.244Z',
    department: 'Marketing',
    priority: 'Hign',
    attachment: 5,
  },
  {
    title: 'Data Gathering From Finance',
    assigned_to: 'Nishant Singh',
    created_date: '2024-08-17T09:57:33.244Z',
    end_date: '2024-10-30T09:57:33.244Z',
    department: 'Finance',
    priority: 'Low',
    attachment: 2,
  },
];

export const userData = [
  {
    id: 857,
    firstName: 'Akash',
    lastName: 'Nigam',
    email: '<EMAIL>',
  },
  {
    id: 856,
    firstName: 'Abhijeet',
    lastName: 'Sharma',
    email: '<EMAIL>',
  },
  {
    id: 855,
    firstName: 'Rini',
    lastName: 'Gupta',
    email: '<EMAIL>',
  },
  {
    id: 852,
    firstName: 'Anand',
    lastName: 'Chaubey',
    email: '<EMAIL>',
  },
  {
    id: 731,
    firstName: 'Ashutosh',
    lastName: 'Singh',
    email: '<EMAIL>',
  },
  {
    id: 699,
    firstName: 'Khushi',
    lastName: 'Chaurasia',
    email: '<EMAIL>',
  },
  {
    id: 681,
    firstName: 'Swati',
    lastName: 'Chandel',
    email: '<EMAIL>',
  },
];

export const Assignee = [
  {
    value: '857',
    label: 'Akash',
  },
  {
    value: '856',
    label: 'Abhijeet',
  },
  {
    value: '855',
    label: 'Rini',
  },
  {
    value: '852',
    label: 'Anand',
  },
  {
    value: 731,
    label: 'Ashutosh',
  },
  {
    value: '699',
    label: 'Khushi',
  },
  {
    value: '681',
    label: 'Swati',
  },
];
export const departmentsData = [
  { id: 1, name: 'HR' },
  { id: 2, name: 'IT' },
  { id: 3, name: 'Support' },
  { id: 4, name: 'Marketing' },
];
