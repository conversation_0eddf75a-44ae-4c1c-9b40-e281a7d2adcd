import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../../../../@/components/ui/AlertDialog';
import { Button } from '../../../../@/components/ui/Common/Elements/Button/Button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../../../../@/components/ui/Common/Elements/Form/Form';
import { Input } from '../../../../@/components/ui/Common/Elements/Input/Input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../@/components/ui/Common/Elements/Select/Select';
import { MultiSelect } from '../../../../@/components/ui/multi-select';
import { Textarea } from '../../../../@/components/ui/textarea';
import httpClient from '../../../../api/httpClientNew';
import { RootState } from '../../../../redux/store';
import {
  TASK_TYPE,
  UpdateTaskModalFormData,
  UpdateTaskModalTaskViewProperties,
  UpdateTaskModalUser,
} from '../../../../types/global-workflow';
import { FETCH_ENTITIES } from '../../../common/api';
import { fetchAssignees } from '../../../common/services/data-subject-request';

const UpdateTaskModal: React.FC<UpdateTaskModalTaskViewProperties> = ({
  handleUpdate,
  showTaskView,
  setShowTaskView,
  data,
  setAddTaskModalOpen,
  setSelectAssignee,
  isTaskOpen,
  automationList,
}) => {
  const [userData, setUserData] = useState<UpdateTaskModalUser[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [entities, setEntities] = useState<any[]>([]);
  const [taskType, setTaskType] = useState('1');
  const [selectedAssigneeIds, setSelectedAssigneeIds] = useState<string[]>([]);
  const group_id = useSelector((state: RootState) => state.globalWorkflow.workflow.group_id);
  const customer_id = useSelector(
    (state: RootState) => state?.auth?.login?.login_details?.customer_id
  );
  const { t } = useTranslation();

  const form = useForm<UpdateTaskModalFormData>({
    defaultValues: {
      title: '',
      guidance_text: '',
      // due_days: 1,
      // assignee_id: [],
    },
  });

  const options = userData.map((user: UpdateTaskModalUser) => ({
    label: `${user.firstName} ${user.lastName}`,
    value: user.id.toString(),
  }));

  // Fetch entities as fallback for group_id
  useEffect(() => {
    const fetchEntities = async () => {
      if (!group_id && customer_id) {
        try {
          console.log('Update Modal - Fetching entities for customer_id:', customer_id);
          const response = await httpClient.get(`${FETCH_ENTITIES}${customer_id}`);
          if (response?.data?.result?.rows?.length > 0) {
            setEntities(response.data.result.rows);
            console.log('Update Modal - Entities fetched:', response.data.result.rows);
          }
        } catch (err) {
          console.error('Update Modal - Error fetching entities:', err);
        }
      }
    };
    fetchEntities();
  }, [customer_id, group_id]);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        // Use group_id from Redux state, or fallback to first entity id
        const effectiveGroupId = group_id || (entities.length > 0 ? entities[0].id : null);

        if (effectiveGroupId) {
          console.log(
            'Update Modal - Fetching assignees for effective group_id:',
            effectiveGroupId
          );
          const response = await fetchAssignees(effectiveGroupId);
          console.log('Update Modal - Assignees response:', response);
          setUserData(response || []);
        } else {
          console.log('Update Modal - No effective group_id available');
          setUserData([]);
        }
      } catch (err) {
        console.error('Update Modal - Error fetching assignees:', err);
        setUserData([]);
      }
    };
    fetchUserData();
  }, [group_id, entities]);

  useEffect(() => {
    if (data) {
      console.log('Resetting form with data:', data, 'userData loaded:', userData.length > 0);
      const assigneeIds = Array.isArray(data.assignee_id)
        ? data.assignee_id.map((id: any) => String(id))
        : [];

      console.log('Processed assignee_ids:', assigneeIds);
      setSelectedAssigneeIds(assigneeIds);
      setTaskType(data?.task_type || '1');
      form.reset({
        title: data?.title || '',
        guidance_text: data?.guidance_text || '',
        due_days: data?.due_days || 0,
        assignee_id: assigneeIds,
        task_type: data?.task_type || '1',
        activepieces_automation_id: data?.activepieces_automation_id ?? undefined,
      });
    }
  }, [data, form]);

  console.log('data--global', data, userData, form);
  const onSubmit = async (formData: UpdateTaskModalFormData) => {
    if (formData.task_type == '1') {
      formData = { ...formData, activepieces_automation_id: null };
    }
    setIsSubmitting(true);
    try {
      await handleUpdate({ ...data, ...formData });
      setShowTaskView(false);
    } catch (error) {
      console.error('Form submission failed', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AlertDialog open={showTaskView} onOpenChange={setShowTaskView}>
      <AlertDialogContent className="max-h-[90%] overflow-auto sm:max-w-[50%]">
        <AlertDialogHeader>
          <AlertDialogTitle>{t('DSR.WorkFlow.UpdateTask')}</AlertDialogTitle>
        </AlertDialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="col-span-2 gap-4">
              <div className="mt-2 flex gap-4">
                <div className="w-1/2">
                  <FormField
                    control={form.control}
                    name="title"
                    rules={{ required: 'This field is required' }}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('DSR.WorkFlow.TaskTitle')}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input placeholder={t('DSR.TaskOverView.EnterTaskTitle')} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="w-1/2">
                  <FormField
                    control={form.control}
                    name="task_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('Task Type')}</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                            defaultValue="1"
                            onOpenChange={(value) => setTaskType(form.getValues('task_type'))}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder={t('Select task type')} />
                            </SelectTrigger>
                            <SelectContent>
                              {TASK_TYPE.map((module) => (
                                <SelectItem key={module.value} value={module.value}>
                                  {t(module.label)}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <div className="mt-2 flex gap-4">
                <div className="w-1/2">
                  <FormField
                    control={form.control}
                    name="assignee_id"
                    render={({ field }) => {
                      console.log(
                        'Update Modal MultiSelect render - options:',
                        options,
                        'userData:',
                        userData,
                        'field.value:',
                        field.value,
                        'data.assignee_id:',
                        data?.assignee_id
                      );
                      return (
                        <FormItem className="w-full">
                          <FormLabel>{t('DSR.WorkFlow.AddAssignee')} </FormLabel>
                          <FormControl>
                            <MultiSelect
                              key={`multiselect-${data?.id}-${userData.length}-${selectedAssigneeIds.join(',')}`}
                              options={options}
                              defaultValue={selectedAssigneeIds}
                              placeholder={
                                options.length > 0 ? 'Select Assignee' : 'No assignees available'
                              }
                              onValueChange={(values: string[]) => {
                                console.log('Update Modal MultiSelect onValueChange:', values);
                                setSelectedAssigneeIds(values);

                                const selectedAssignees = values
                                  .map((item) => {
                                    const assignee = userData.find(
                                      (user) => String(user.id) === String(item)
                                    );
                                    return assignee;
                                  })
                                  .filter(Boolean);

                                setSelectAssignee(selectedAssignees as any);
                                field.onChange(values);
                              }}
                              className="h-12 w-full rounded-md border border-solid border-[#CACACA]"
                              variant="secondary"
                              maxCount={5}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                </div>
                <div className="w-1/2">
                  {taskType == '1' ? (
                    <FormField
                      control={form.control}
                      name="due_days"
                      // rules={{
                      //   required: 'This field is required and max due days is 30',
                      //   min: { value: 1, message: 'Minimum value is 1' },
                      //   max: { value: 30, message: 'Maximum value is 30' },
                      // }}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t('DSR.TaskOverView.DueDays')}
                            {/* <span className="text-destructive">*</span> */}
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="Enter days"
                              value={field.value ?? ''}
                              onChange={(e) => {
                                let value = e.target.value === '' ? '' : Number(e.target.value);
                                if (typeof value === 'number') {
                                  if (value > 30) value = 30;
                                  if (value < 1) value = 1;
                                }
                                field.onChange(value);
                              }}
                              onBlur={() => {
                                if (field.value === null) field.onChange('');
                              }}
                              min={1}
                              max={30}
                              className="appearance-none [&::-webkit-inner-spin-button]:hidden [&::-webkit-outer-spin-button]:hidden"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  ) : (
                    <FormField
                      control={form.control}
                      name="activepieces_automation_id"
                      // rules={{required:"Select automation flow"}}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('Automation Workflow')}</FormLabel>
                          <FormControl>
                            <Select
                              value={field.value?.toString() || ''}
                              onValueChange={field.onChange}
                              // defaultValue="1"
                              // disabled={isFormLoading}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder={t('Select automation workflow')} />
                              </SelectTrigger>
                              <SelectContent>
                                {automationList.map((option) => (
                                  <SelectItem key={option?.id} value={option?.id}>
                                    {option?.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
              </div>

              <div className="mt-2">
                <FormField
                  control={form.control}
                  name="guidance_text"
                  rules={{
                    required: 'This field is required',
                    maxLength: {
                      value: 500,
                      message: 'Maximum 500 characters allowed',
                    },
                  }}
                  render={({ field }) => (
                    <FormItem className="col-span-2 mt-4">
                      <FormLabel>
                        {t('DSR.WorkFlow.GuidanceText')}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Textarea
                            placeholder="Enter Note"
                            {...field}
                            onChange={(e) => {
                              if (e.target.value.length <= 500) {
                                field.onChange(e);
                              }
                            }}
                          />
                          <div className="absolute bottom-2 right-2 text-sm text-muted-foreground">
                            {field.value?.length || 0}/500
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <AlertDialogFooter className="flex justify-end">
              <Button type="button" variant="secondary" onClick={() => setShowTaskView(false)}>
                {t('Common.Cancel')}
              </Button>
              <Button
                type="submit"
                className="bg-custom-primary text-white hover:bg-custom-primary hover:text-white"
                disabled={isSubmitting}
              >
                {isSubmitting ? t('Common.Updating') : t('Common.Update')}
              </Button>
            </AlertDialogFooter>
          </form>
        </Form>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default UpdateTaskModal;
