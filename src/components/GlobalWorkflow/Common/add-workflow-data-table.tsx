import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import React, { useState } from 'react';
import { SkeletonCard } from '../../../@/components/ui/Common/Elements/Skeleton/Skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../@/components/ui/Common/Table/Table';
import {
  AddWorkflowDataTableProperties,
  AddWorkFlowDataTableUserDetailsUser,
} from '../../../types/global-workflow';
import UpdateTaskModal from './Modal/update-task-modal';

const AddWorkflowDataTable: React.FC<AddWorkflowDataTableProperties> = ({
  setShowTaskView,
  showTaskView,
  handleUpdate,
  data,
  columns,
  loading,
  isTaskOpen,
  isEditMode,
  automationList,
}) => {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [selectAssignee, setSelectAssignee] = useState<AddWorkFlowDataTableUserDetailsUser[]>([]);
  const [selectedTask, setSelectedTask] = useState<any>(null);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    state: {
      sorting,
    },
  });

  const handleRowClick = (taskData: any) => {
    setSelectedTask(taskData);
    setShowTaskView(true);
  };

  return (
    <>
      {isEditMode && (
        <UpdateTaskModal
          handleUpdate={handleUpdate}
          setShowTaskView={setShowTaskView}
          showTaskView={showTaskView}
          data={selectedTask}
          setAddTaskModalOpen={() => true}
          setSelectAssignee={setSelectAssignee}
          isTaskOpen={isTaskOpen}
          automationList={automationList}
        />
      )}
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                return (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? undefined
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => {
              return (
                <TableRow
                  className="w-full cursor-pointer"
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  onClick={() => handleRowClick(row.original)}
                >
                  {row.getVisibleCells().map((cell) => {
                    return (
                      <TableCell key={cell.id} className="p-5">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    );
                  })}
                </TableRow>
              );
            })
          ) : loading ? (
            <TableRow>
              <TableCell colSpan={columns.length}>
                <SkeletonCard />
                <SkeletonCard />
                <SkeletonCard />
                <SkeletonCard />
                <SkeletonCard />
              </TableCell>
            </TableRow>
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className="h-24 text-center">
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </>
  );
};

export default AddWorkflowDataTable;
