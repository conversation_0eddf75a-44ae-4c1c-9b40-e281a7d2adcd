import * as React from 'react';
import { useEffect, useState } from 'react';
import { SkeletonCard } from '../../@/components/ui/Common/Elements/Skeleton/Skeleton';
import greyCircle from '../../assets/GreyEllipse.svg';
import { getInitialsByName } from '../../utils/helperData';
import AvatarFrame from '../common/Avatar';
import { convertDateToHumanView } from '../common/CommonHelperFunctions';
import TablePaginationDemo from '../common/Pagenation';
import { Fetch_User_Management_Audit_Log } from '../common/services/userManagement';

interface AuditData {
  id: number;
  action: string;
  createdAt: string;
  name: string;
  user_email?: string;
}

interface AuditResponse {
  count: number;
  rows: AuditData[];
}

interface UserManagementAuditLogProps {
  searchValue: string;
}

const UserManagementAuditLog: React.FC<UserManagementAuditLogProps> = ({ searchValue }) => {
  const [isLoading, setLoading] = useState(false);
  const [auditData, setAuditData] = useState<AuditResponse | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [numberOfItems, setNumberOfItems] = useState(10);

  function handleItemsChange(value: number) {
    setNumberOfItems(value);
  }

  function handlePageChange(value: number) {
    setCurrentPage(value);
  }

  useEffect(() => {
    const fetchAuditLog = async () => {
      setLoading(true);
      if (searchValue) {
        setCurrentPage(1);
      }

      try {
        const response = await Fetch_User_Management_Audit_Log(
          currentPage,
          numberOfItems,
          searchValue
        );

        if (response && response.data) {
          const auditLogData = response.data.result || response.data;
          setAuditData({
            count: auditLogData.count || 0,
            rows: auditLogData.rows || [],
          });
        } else {
          setAuditData({ count: 0, rows: [] });
        }
      } catch (error) {
        console.error('Error fetching user management audit log:', error);
        setAuditData({ count: 0, rows: [] });
      } finally {
        setLoading(false);
      }
    };

    fetchAuditLog();
  }, [currentPage, numberOfItems, searchValue]);

  return (
    <div className="flex min-h-screen flex-col rounded-lg bg-white">
      <div className="flex-grow overflow-auto p-4">
        {isLoading ? (
          <>
            <SkeletonCard />
            <SkeletonCard />
            <SkeletonCard />
            <SkeletonCard />
            <SkeletonCard />
          </>
        ) : (
          <>
            {auditData?.rows && auditData.rows.length > 0 ? (
              auditData.rows.map((item) => (
                <div
                  key={item.id}
                  className="mb-3 flex w-full items-center justify-between rounded-lg border border-[#C0CDE0] bg-white px-[6px] py-[7px] text-base leading-normal text-[#999] lg:px-[13px]"
                >
                  <div className="flex items-center gap-1">
                    <AvatarFrame value={item?.name} getInitials={getInitialsByName} />
                    <div className="flex flex-col">
                      <span className="font-medium">{item?.name}</span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <p className="pl-14 pr-10 font-medium">{item?.action}</p>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2.5 whitespace-nowrap">
                      <img src={greyCircle} alt="Grey Circle Dot" />
                      {convertDateToHumanView(item?.createdAt)}
                    </div>
                    <div className="flex items-center gap-2.5 whitespace-nowrap">
                      <img src={greyCircle} alt="Grey Circle Dot" />
                      {new Date(item?.createdAt).toLocaleTimeString('en-US', {
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="flex flex-row justify-center py-8">
                <div className="text-gray-500">
                  {searchValue
                    ? 'No audit logs found matching your search.'
                    : 'No audit logs found.'}
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {auditData && auditData.count > 0 && (
        <div className="sticky bottom-1 z-10 border-t border-gray-200 p-4">
          <TablePaginationDemo
            count={auditData.count}
            currentPage={currentPage}
            numOfItems={numberOfItems}
            handleItemsChange={handleItemsChange}
            handlePageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
};

export default UserManagementAuditLog;
