import search from '../../assets/Search.svg';
import addNew from '../../assets/plusIconNew.svg';

import { useQuery } from '@tanstack/react-query';
import { debounce } from 'lodash';
import moment from 'moment';
import { ChangeEvent, useCallback, useEffect, useState } from 'react';
import { DateRange } from 'react-day-picker';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { createGlobalStyle } from 'styled-components';
import { DatePickerWithRange } from '../../@/components/ui/Common/Elements/Date/DateRangePicker';
import { Label } from '../../@/components/ui/Common/Elements/Label/Label';
import { fetchUserManagementList } from '../../redux/actions/UserManagement/UserManagementActions';
import { userManagementActions } from '../../redux/reducers/userManagement/userManagementSlice';
import TablePaginationDemo from '../common/Pagenation';
import UserDataTable from './Table/DataTable';
import './UserManagementTable.css';

const tableHeadings = [
  { id: 'firstName', label: 'First Name', sort: false, width: 'w-[10%]' },
  { id: 'lastName', label: 'Last Name', sort: false, width: 'w-[10%]' },
  { id: 'email', label: 'E-Mail', sort: false, width: 'w-[15%]' },
  { id: 'phone', label: 'Contact Number', sort: false, width: 'w-[14%]' },
  { id: 'createdAt', label: 'Added Date', sort: true, width: 'w-[10%]' },
  { id: 'updatedAt', label: 'Last Updated', sort: true, width: 'w-[10%]' },
  { id: 'group', label: 'Groups', sort: false, width: 'w-[12%]' },
  { id: 'role_name', label: 'Role', sort: false, width: 'w-[8%]' },
  { id: 'action', label: 'Action', sort: false, width: 'w-[5%]' },
];

interface UserManagementTableProperties {
  handleChangePreviousPath: (value: string, event: any) => void;
  handleCurrentPageChange: (value: string, event: any) => void;
  handleUserIdChange: (value: number, event: any) => void;
}

const GlobalStyle = createGlobalStyle`
  .react-datepicker-wrapper {
    /* Add your styles here */
    width: 100%;
  }

  .react-datepicker__input-container {
    /* Add your styles here */
    /* width: calc(100% + 28px); */
    padding-left: 5px;
  }

  .react-datepicker__input-container > input:focus {
    outline: none;
    box-shadow: none;
    border-color: transparent;
`;

const addType = 'User';

const UserManagementTable: React.FC<UserManagementTableProperties> = ({
  handleChangePreviousPath,
  handleCurrentPageChange,
  handleUserIdChange,
}) => {
  const dispatch = useDispatch();

  const loginData = useSelector((state: any) => state.auth.login.login_details);

  const endDate = useSelector((state: any) => state.userManagement.userManagementData.endDate);
  const [selectedDates, setSelectedDates] = useState<[Date | null, Date | null]>([null, null]);
  const startDate = useSelector((state: any) => state.userManagement.userManagementData.startDate);

  const count = useSelector((state: any) => state.userManagement.userManagementData.count);
  const tableData = useSelector((state: any) => state.userManagement.userManagementData.rows);
  const numberOfItems = useSelector(
    (state: any) => state.userManagement.userManagementData.itemsPerPage
  );
  const currentPage = useSelector(
    (state: any) => state.userManagement.userManagementData.currentPage
  );

  const [isLoading, setIsLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [sortType, setSortType] = useState<any>({
    key: 'createdAt',
    order: 'DESC',
  });
  const [date, setDate] = useState<DateRange | undefined>({
    from: undefined,
    to: undefined,
  });

  function handleNumberOfPages(value: number) {
    dispatch(userManagementActions.setUserManagementItemsPerPage(value));
  }

  function handlePageChange(value: number) {
    dispatch(userManagementActions.setUserManagementDataCurrentPage(value));
  }

  function handleSortType(item: any) {
    if (item.sort) {
      const id = item.id;

      if (sortType && sortType.key === id) {
        const newOrder = sortType.order === 'ASC' ? 'DESC' : 'ASC';
        setSortType({ key: id, order: newOrder });
      } else {
        setSortType({ key: id, order: 'DESC' });
      }
    }
  }

  const handleSearchValue = useCallback(
    debounce((event: ChangeEvent<HTMLInputElement>) => {
      setSearchValue(event.target.value);
    }, 1000), // delay in ms
    []
  );

  let customer_id = 0;

  if (loginData !== null) {
    customer_id = loginData.customer_id;
  }

  const dateFormatter = (isoTimestamp: string) => {
    const dateObject = new Date(isoTimestamp);
    const formattedDate = dateObject.toISOString().slice(0, 10);

    return formattedDate;
  };

  const fetchUserData = async () => {
    let url = `/api/v1/user/list?customer_id=${customer_id}`;

    if (searchValue !== '') {
      url += `&search=${searchValue}`;
    }

    if (sortType !== undefined) {
      url += `&sort_by=${sortType.key}&sort_order=${sortType.order}`;
    }

    if (startDate instanceof Date && endDate instanceof Date) {
      const start = moment(startDate).format('YYYY-MM-DD');
      const end = moment(endDate).format('YYYY-MM-DD');
      url += `&start_date=${start}&end_date=${end}`;
    }

    if (startDate && !endDate) {
      return;
    }

    dispatch(userManagementActions.setUserManagementData({ count: 0, rows: [] }));

    try {
      const resData = await dispatch<any>(fetchUserManagementList(url));
      const userManagementListData = resData.result;

      const formattedData = {
        rows: userManagementListData.rows.map((item: any) => ({
          id: item.id ?? '',
          status: item.status ?? '',
          firstName: item.firstName ?? '',
          lastName: item.lastName ?? '',
          email: item.email ?? '',
          phone: item.phone ?? '',
          country_code: item.country_code ?? '',
          addedDate: dateFormatter(item.createdAt) ?? '',
          updatedDate: dateFormatter(item.updatedAt) ?? '',
          group: item.group_data && item.group_data.length > 0 ? item.group_data[0].name : '',
          role_name: item.Role ? item.Role.role_name : '',
          firstLogin: item.firstLogin ?? '',
        })),
        count: userManagementListData.count ?? 0,
      };
      dispatch(userManagementActions.setUserManagementData(userManagementListData));
      return formattedData;
    } catch (error) {
      console.error('Error fetching user data:', error);
      return { rows: [], count: 0 };
    }
  };

  const { data: responseData, isLoading: queryLoading } = useQuery({
    queryKey: [
      'userManagement',
      numberOfItems,
      currentPage,
      searchValue,
      sortType,
      startDate,
      endDate,
    ],
    queryFn: fetchUserData,
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    date?.from
      ? dispatch(userManagementActions.setStartDate(date?.from))
      : dispatch(userManagementActions.setStartDate(null));
    date?.to
      ? dispatch(userManagementActions.setEndDate(date?.to))
      : dispatch(userManagementActions.setEndDate(null));
  }, [date]);

  const { t } = useTranslation();

  return (
    <div className="size-full">
      <div className="w-full rounded-md font-[Poppins]">
        <div className="items-flex-end flex flex-col gap-4 p-3">
          <div className="flex-start flex h-11 w-full justify-between self-stretch">
            <div className="flex h-full gap-5">
              <div className="flex h-full w-[222px] flex-[1,0,0] items-center justify-between rounded-md border border-solid border-input px-3">
                <input
                  type="text"
                  id="searchBar"
                  name="searchBar"
                  placeholder={t('Common.Search')}
                  className="w-full bg-transparent font-[Poppins] text-sm font-light leading-normal outline-none"
                  onChange={(e) => handleSearchValue(e)}
                />
                <img src={search} alt="search sign" className="size-[19px] shrink-0" />
              </div>
              <DatePickerWithRange
                date={date}
                setDate={setDate}
                className="flex h-11 w-[295px] items-center justify-between rounded-md border border-solid border-input bg-transparent font-primary-text text-[13px] font-light text-primary"
              />
            </div>

            <button
              className="flex h-11 w-auto items-center justify-center gap-2 rounded-lg bg-custom-primary px-[15px] py-[11px]"
              onClick={(event) => handleCurrentPageChange('addNew', event)}
            >
              <div className="flex items-center justify-center gap-2">
                <img src={addNew} alt="right arrow sign" className="aspect-square h-5" />
                <span className="font-[Poppins] text-sm font-medium leading-[29px] text-white">
                  {t('UserManagement.UserTable.AddUser')}
                </span>
              </div>
            </button>
          </div>
          <div className="flex flex-row justify-start gap-[31px] pl-2">
            <div className="flex flex-row items-center justify-between gap-1">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="8"
                height="8"
                viewBox="0 0 8 8"
                fill="none"
              >
                <circle cx="3.99537" cy="3.74537" r="3.49537" fill="#00CBA0" />
              </svg>
              <Label className="text-[10px] leading-[13.981px] text-[#262626]">
                {t('RoleManagement.ActiveStatus.Active')}
              </Label>
            </div>

            <div className="flex flex-row items-center justify-between gap-1">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="8"
                height="8"
                viewBox="0 0 8 8"
                fill="none"
              >
                <circle cx="3.99537" cy="3.74537" r="3.49537" fill="#FF9950" />
              </svg>
              <Label className="text-[10px] leading-[13.981px] text-[#262626]">
                {t('RoleManagement.ActiveStatus.Inactive')}
              </Label>
            </div>

            <div className="flex flex-row items-center justify-between gap-1">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="8"
                height="8"
                viewBox="0 0 8 8"
                fill="none"
              >
                <circle cx="3.99537" cy="3.74537" r="3.49537" fill="#FA1464" />
              </svg>
              <Label className="text-[10px] leading-[13.981px] text-[#262626]">
                {t('RoleManagement.ActiveStatus.Archived')}
              </Label>
            </div>
          </div>

          <div
            className="table_main_content mt-0 w-full overflow-auto scrollbar-hide"
            style={{ height: 'calc(100vh - 18rem)' }}
          >
            {/* <UserTable
              handleChangePreviousPath={handleChangePreviousPath}
              handleCurrentPageChange={handleCurrentPageChange}
              handleUserIdChange={handleUserIdChange}
              tableHeadings={tableHeadings}
              handleSortType={handleSortType}
              rowData={tableData}
              loading={isLoading}
            /> */}
            <div className="flex-grow overflow-auto">
              <UserDataTable
                handleChangePreviousPath={handleChangePreviousPath}
                handleCurrentPageChange={handleCurrentPageChange}
                handleUserIdChange={handleUserIdChange}
                tableHeadings={tableHeadings}
                handleSortType={handleSortType}
                data={responseData?.rows ?? []}
                loading={queryLoading}
              />
            </div>
          </div>
        </div>
        {/* <div className="flex flex-row justify-between items-center"> */}
        <div className="sticky bottom-1 z-10 border-t bg-white">
          <TablePaginationDemo
            count={count}
            handleItemsChange={handleNumberOfPages}
            handlePageChange={handlePageChange}
            currentPage={currentPage}
            numOfItems={numberOfItems}
          />
        </div>
      </div>
    </div>
  );
};

export default UserManagementTable;
