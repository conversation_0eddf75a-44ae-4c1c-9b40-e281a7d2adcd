import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { SkeletonCard } from '../../../@/components/ui/Common/Elements/Skeleton/Skeleton';
import greyCircle from '../../../assets/GreyEllipse.svg';
import { fetchPolicyAuditLogData } from '../../../redux/actions/CreatePolicyActions/CreatePolicyActions';
import { createPolicyActions } from '../../../redux/reducers/CreatePolicy/CreatePolicySlice';
import { FETCH_ALL_POLICY_AUDIT_LOG } from '../../common/api';
import AvatarFrame from '../../common/Avatar';
import { convertDateToHumanView } from '../../common/CommonHelperFunctions';
import TablePaginationDemo from '../../common/Pagenation';
import CommentModal from '../../PrivacyAndDataProtection/CreatePolicy/CreatePolicyComponents/CommentModal/Comment';
import Modal from '../../UI/Modal';

export default function AuditLog() {
  const dispatch = useDispatch();

  const count = useSelector((state: any) => state.createPolicy.policyAuditLog.count);
  const auditLogData = useSelector((state: any) => state.createPolicy.policyAuditLog.rows);
  const numberOfItems = useSelector((state: any) => state.createPolicy.policyAuditLog.itemsPerPage);
  const currentPage = useSelector((state: any) => state.createPolicy.policyAuditLog.currentPage);
  const createPolicyId = useSelector((state: any) => state.policyManagement.createdPolicyId);

  // const [ count, setCount ] = useState(0);
  // const [ auditLogData, setAuditLogData ] = useState([]);
  // const [ numOfItems, setNumOfItems ] = useState(10);
  // const [ currentPage, setCurrentPage ] = useState(1);

  const [isLoading, setIsLoading] = useState(false);
  const [currentComment, setCurrentComment] = useState('');
  const [currentUserType, setCurrentUserType] = useState('');
  const [currentCommentBy, setCurrentCommentBy] = useState('');
  const [modalIsOpen, setModalIsOpen] = useState<boolean>(false);

  function handleNumberOfPages(value: number) {
    dispatch(createPolicyActions.setAuditLogItemsPerPage(value));
    // setNumOfItems(value);
  }

  function handlePageChange(value: number) {
    dispatch(createPolicyActions.setAuditLogCurrentPage(value));
    // setCurrentPage(value);
  }

  function handleOpen(comment: string, commentBy: string, action: string) {
    const type = action.split(' ');
    setCurrentUserType(action.split(' ')[type.length - 1]);
    setCurrentCommentBy(commentBy);
    setCurrentComment(comment);
    setModalIsOpen(true);
  }

  useEffect(() => {
    setIsLoading(true);
    const fetchData = async () => {
      // const decipherId = decryptId(createPolicyId);
      const url = `${FETCH_ALL_POLICY_AUDIT_LOG}?page=${currentPage}&size=${numberOfItems}`;
      // let url = `${FETCH_ALL_POLICY_AUDIT_LOG}`;
      dispatch(createPolicyActions.setAuditLogData({ count: 0, rows: [] }));

      try {
        const resData = await dispatch<any>(fetchPolicyAuditLogData(url));
        console.log('test74', resData);
        const auditLogList = resData.result;

        dispatch(createPolicyActions.setAuditLogData(auditLogList || { count: 0, rows: [] }));
        setIsLoading(false);
      } catch (error) {
        if (error instanceof Error) {
          // console.log("Fetching Audit Log data failed!");
        } else {
          // console.log("An unknown error occurred:", error);
          // Handle other types of errors if needed
        }
        setIsLoading(false);
      }
    };
    fetchData();
  }, [numberOfItems, currentPage]);

  function getInitialsByName(namePart: string): string {
    const fullNamePart = namePart?.split(' ');

    let initials = fullNamePart?.map((name) => {
      if (name.length > 0) {
        return name[0].toUpperCase();
      }
      return '';
    });

    initials = initials.filter((initial) => initial !== '');

    if (initials.length > 2) {
      initials = [initials[0], initials.at(-1) ?? ''];
    }

    return initials.join('');
  }
  return (
    <div style={{ height: 'calc(100vh - 18rem)' }}>
      {modalIsOpen && (
        <Modal
          open={modalIsOpen}
          onClose={() => setModalIsOpen(false)}
          cssClass="w-[31.7%] shrink-0 rounded-2xl bg-white shadow-[10px_20px_30px_0px_rgba(0,0,0,0.10)]"
        >
          <CommentModal
            type={currentUserType}
            comment={currentComment}
            commentBy={currentCommentBy}
            onClose={() => setModalIsOpen(false)}
          />
        </Modal>
      )}
      <div className="flex size-full shrink-0 flex-col gap-3 overflow-auto scrollbar-hide">
        {isLoading && auditLogData.length === 0 && (
          <>
            <SkeletonCard />
            <SkeletonCard />
            <SkeletonCard />
            <SkeletonCard />
            <SkeletonCard />
          </>
        )}
        {!isLoading && auditLogData.length === 0 && (
          <div className="flex w-full flex-row items-center justify-center font-[Poppins]">
            Data Not Found!
          </div>
        )}

        {auditLogData.map((item: any, index: number) => (
          <div
            key={item.id}
            className="mb-3 flex w-full items-center justify-between rounded-lg border border-[#C0CDE0] bg-white px-[6px] py-[7px] text-base leading-normal text-[#999] lg:px-[13px]"
          >
            <div className="flex items-center gap-1">
              <AvatarFrame value={item?.initials} />
              <div className="flex flex-col">
                <span className="font-medium">{item?.name}</span>
              </div>
            </div>

            <div className="flex-1">
              <p className="pl-14 pr-10 font-medium">{item?.action}</p>
            </div>

            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2.5 whitespace-nowrap">
                <img src={greyCircle} alt="Grey Circle Dot" />
                {convertDateToHumanView(item?.createdAt)}
              </div>
              <div className="flex items-center gap-2.5 whitespace-nowrap">
                <img src={greyCircle} alt="Grey Circle Dot" />
                {new Date(item?.createdAt).toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </div>
            </div>
          </div>
        ))}
      </div>

      <TablePaginationDemo
        count={count}
        handleItemsChange={handleNumberOfPages}
        handlePageChange={handlePageChange}
        currentPage={currentPage}
        numOfItems={numberOfItems}
      />
    </div>
  );
}
