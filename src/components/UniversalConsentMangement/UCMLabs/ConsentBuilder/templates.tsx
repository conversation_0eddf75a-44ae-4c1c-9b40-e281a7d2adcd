import { ColumnDef } from '@tanstack/react-table';
import { debounce } from 'lodash';
import { ArrowUpDown, Link } from 'lucide-react';
import { ChangeEvent, useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../../../@/components/ui/Common/Elements/Button/ButtonSort';
import { Input } from '../../../../@/components/ui/Common/Elements/Input/Input';
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuTrigger,
// } from '../../../@/components/ui/dropdown-menu';
// import action from '../../../../assets/images/more-horizontal.png';
import { TooltipProvider } from '@radix-ui/react-tooltip';
import { useMutation, useQuery } from '@tanstack/react-query';
import axios from 'axios';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../@/components/ui/Common/Elements/Select/Select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../../../@/components/ui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipTrigger } from '../../../../@/components/ui/tooltip';
import httpClient from '../../../../api/httpClientNew';
import action from '../../../../assets/images/more-horizontal.png';
import addIcon from '../../../../assets/plusSign.svg';
import searchIcon from '../../../../assets/Search.svg';
import { universalConsentManagementActions } from '../../../../redux/reducers/UniversalConsentManagement/universal-consent-management-slice';
import { RootState } from '../../../../redux/store';
import {
  CollectionTemplateData,
  EntityProperties,
  FormConfiguration,
  PreferenceFormData,
  SubjectIdentityDetailsProperties,
} from '../../../../types/universal-consent-management';
import { getInitialsByName } from '../../../../utils/helperData';
import { UCM_ADD_COLLECTION_TEMPLATE } from '../../../../utils/routeConstant';
import { UCM_REMOVE_COLLECTION_TEMPLATE } from '../../../common/api';
import AvatarFrame from '../../../common/Avatar';
import { convertDateToHumanView } from '../../../common/CommonHelperFunctions';
import {
  change_collection_template,
  checkUnifiedPiiInCollectionTemplate,
  fetchCollectionTemplateData,
  get_entities,
  get_preference_center_data,
} from '../../../common/services/universal-consent-management';
import ShadcnDialog from '../../../common/shadcn-dialog';
import DynamicTable from '../../../common/ShadcnDynamicTable/dynamic-table';
import DynamicConsentForm from '../../../Customization/UCM-Form/dynamic-consent-form';
import GoTrustTitle from './../../../../assets/gotrustTitle_light.svg';
import { DataType, PiiValuesType, ResponseType } from './ConsentBuilderSteps/source-step';
import PreferenceFormView from './PreferenceFormView/prefernce-form-view';

const Templates = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  //! Variables
  const customer_id: number | undefined = useSelector(
    (state: RootState) => state?.auth?.login?.login_details?.customer_id
  );
  const filter = 'all';
  const tabData = ['Basic Info', 'Consent Collection Form', 'Preference Form', 'Code Snippets'];
  const formTabData = ['Web View', 'Mobile View'];
  const preferenceTabData = ['Web View', 'Mobile View'];
  // const count = 10;
  // const numberOfItems = 10;
  // const currentPage = 1;
  const { t } = useTranslation();

  const columns: ColumnDef<CollectionTemplateData>[] = [
    {
      accessorKey: 'name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            {t('ucm.CollectionBuilder.TemplateName')}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const name: string = row?.original?.name;

        return <div className="">{name ?? '-'}</div>;
      },
    },
    {
      accessorKey: 'subject_identity_type_name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            {t('ucm.CollectionBuilder.DataIdentifierType')}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const type: string = row?.original?.subject_identity_type_name;

        return <div className="">{type ?? '-'}</div>;
      },
    },
    {
      accessorKey: 'entity_name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            {t('ucm.CollectionBuilder.EntityName')}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const entity_name: string = row?.original?.entity_name;

        return <div className="">{entity_name ?? '-'}</div>;
      },
    },
    {
      accessorKey: 'owner_name',
      header: ({ column }) => {
        return (
          <div className="text-left">
            {' '}
            <Button
              variant="ghost"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
              className="p-0 text-left"
            >
              {t('ucm.CollectionBuilder.Owner')}
              <ArrowUpDown className="ml-2 size-4" />
            </Button>
          </div>
        );
      },
      cell: ({ row }) => {
        const owner = row?.original?.owner_name;

        return owner ? (
          <div className="flex items-center gap-2">
            <AvatarFrame value={owner} getInitials={getInitialsByName} />
            <span className="">{owner}</span>
          </div>
        ) : (
          <div>-</div>
        );
      },
    },
    {
      accessorKey: 'created_at',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            {t('ucm.CollectionBuilder.CreatedDate')}
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const created_at: string | null = row?.original?.created_at;

        return (
          <div className="">{created_at ? convertDateToHumanView(created_at.toString()) : '-'}</div>
        );
      },
    },
    {
      accessorKey: 'updated_at',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            {t('ucm.CollectionBuilder.ModifiedDate')}
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const modified_date: string | null = row?.original?.updated_at;

        return (
          <div className="">
            {modified_date ? convertDateToHumanView(modified_date.toString()) : '-'}
          </div>
        );
      },
    },
    {
      accessorKey: 'form_url',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            {t('ucm.CollectionBuilder.FormURL')}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const form_url: string | null = row?.original?.form_url;

        if (!form_url) {
          return <div>-</div>;
        }

        const copyToClipboard = (event: React.MouseEvent) => {
          event.stopPropagation(); // Prevents propagation to parent events
          navigator.clipboard.writeText(form_url);
          toast.dismiss();
          toast.success(t('FrontEndErrorMessage.UniversalConsentManagement.urlCopiedToClipboard'));
        };

        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <button onClick={copyToClipboard} className="flex items-center p-2">
                  <Link className="h-4 w-4 cursor-pointer text-blue-500" />
                </button>
              </TooltipTrigger>
              <TooltipContent className="text-white">
                <p>{form_url}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      },
    },
    {
      accessorKey: 'preference_centre_url',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            {t('ucm.CollectionBuilder.CenterURL')}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const preference_centre_url: string | null = row?.original?.preference_centre_url;

        if (!preference_centre_url) {
          return <div>-</div>;
        }

        const copyToClipboard = (event: React.MouseEvent) => {
          event.stopPropagation(); // Prevents propagation to parent events
          navigator.clipboard.writeText(preference_centre_url);
          toast.dismiss();
          toast.success(t('FrontEndErrorMessage.UniversalConsentManagement.urlCopiedToClipboard'));
        };

        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <button onClick={copyToClipboard} className="flex items-center p-2">
                  <Link className="h-4 w-4 cursor-pointer text-blue-500" />
                </button>
              </TooltipTrigger>
              <TooltipContent className="text-white">
                <p>{preference_centre_url}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      },
    },
    {
      accessorKey: 'active_status',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="p-0"
          >
            {t('ucm.CollectionBuilder.Status')}
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const active_status: boolean = row?.original?.active_status;

        return <div className="">{active_status ? 'Active' : 'Inactive'}</div>;
      },
    },
    {
      header: t('ucm.CollectionBuilder.Action'),
      accessorKey: 'action',
      cell: ({ row }) => {
        const currentFormId = row?.original?.form_id;
        const currentSourceId = row?.original?.form_source_id;
        const preferenceId = row?.original?.preference_center_id;
        const collectionTemplateId = row?.original?.id;
        const handleStatusChange = async (status: boolean, event: React.MouseEvent) => {
          event.stopPropagation(); // Prevent row click handler from being executed

          try {
            const collectionTemplateId = row.original.id;

            // Make the API call to update the status
            const requestBody = {
              record_id: collectionTemplateId,
              customer_id: customer_id,
              active_status: status,
            };

            const response = await change_collection_template(requestBody);
            if (response.success) {
              toast.success(
                t(
                  status
                    ? 'ToastMessages.General.TemplateActivatedSuccessfully'
                    : 'ToastMessages.General.TemplateInactivatedSuccessfully'
                )
              );
              setReloadCollectionTemplate((prev) => !prev);
              // Optionally refresh the table data or set state to reflect changes
            } else {
              throw new Error(response.data.message || 'Failed to update status');
            }
          } catch (error) {
            console.error('Error updating status:', error);
            toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.failedToUpdateStatus'));
          }
        };

        return (
          <div className="flex items-center gap-4">
            <DropdownMenu>
              <DropdownMenuTrigger>
                <img
                  src={action}
                  alt="action"
                  className="inline-block cursor-pointer hover:bg-muted"
                />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="font-primary-text">
                <DropdownMenuItem onClick={(event) => handleStatusChange(true, event)}>
                  Activate
                </DropdownMenuItem>
                <DropdownMenuItem onClick={(event) => handleStatusChange(false, event)}>
                  Inactivate
                </DropdownMenuItem>
                {currentFormId && currentSourceId && (
                  <DropdownMenuItem
                    onClick={(event) => {
                      event.stopPropagation();

                      setFormId(currentFormId);
                      setSourceId(currentSourceId);
                      setShouldFetchFormData(true);
                      setOpenFormDialog(true);
                    }}
                  >
                    View Consent Form
                  </DropdownMenuItem>
                )}
                {preferenceId && (
                  <DropdownMenuItem
                    onClick={(event) => {
                      event.stopPropagation();
                      setPreferenceCenterId(preferenceId);
                      setShouldFetchPreferenceData(true);
                      dispatch(universalConsentManagementActions.setPreferenceId(preferenceId));
                      dispatch(
                        universalConsentManagementActions.setCollectionTemplateId(
                          collectionTemplateId
                        )
                      );
                      setOpenPreferenceDialog(true);
                    }}
                  >
                    View Preference Center
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem
                  className="text-red-600 hover:text-red-800"
                  onClick={(event) => {
                    event.stopPropagation();
                    setDeletingCollectionTemplateDialogOpen(true);
                    setDeletingCollectionTemplateData({
                      entity_id: row?.original?.entity_id,
                      record_ids: [row?.original?.id],
                      component: 'collection_template',
                      customer_id: customer_id,
                    });
                  }}
                >
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];
  //! STATES
  const [searchTerm, setSearchTerm] = useState<string>();
  const [deletingCollectionTemplateData, setDeletingCollectionTemplateData] = useState({});
  const [deletingCollectionTemplateDialogOpen, setDeletingCollectionTemplateDialogOpen] =
    useState(false);
  const [openFormDialog, setOpenFormDialog] = useState<boolean>(false);
  const [openPreferenceDialog, setOpenPreferenceDialog] = useState<boolean>(false);
  const [formId, setFormId] = useState<number | null>(-1);
  const [sourceId, setSourceId] = useState<number | null>(-1);
  const [shouldFetchFormData, setShouldFetchFormData] = useState<boolean>(false);
  const [preferenceCenterId, setPreferenceCenterId] = useState<number | null>(-1);
  const [shouldFetchPreferenceData, setShouldFetchPreferenceData] = useState<boolean>(false);
  const [preferenceFormData, setPreferenceFormData] = useState<PreferenceFormData>();
  const [subjectIdentityDetails, setSubjectIdentityDetails] =
    useState<SubjectIdentityDetailsProperties>();
  const [error, setError] = useState<string | null>(null);

  console.log({ formId, sourceId }, '1234');

  // const [isLoading, setIsLoading] = useState(false);
  // const [collectionTemplateList, setCollectionTemplateList] = useState([]);
  const [reloadCollectionTemplate, setReloadCollectionTemplate] = useState(true);
  // const [selectedCollectionData, setSelectedCollectionData] = useState<CollectionTemplateData>();
  // const [showModalView, setShowModalView] = useState<boolean>(false);
  // const [activeTab, setActiveTab] = useState<string>('0');
  // const [formActiveTab, setFormActiveTab] = useState<string>('0');
  // const [preferenceActiveTab, setPreferenceActiveTab] = useState<string>('0');
  // const [collectionTemplateData, setCollectionTemplateData] = useState<
  //   CollectionTemplateCompleteData[]
  // >([]);
  // const [frequencyList, setFrequencyList] = useState<FrequencyProperties[]>([]);
  // const [entities, setEntities] = useState<EntityProperties[]>([]);
  const [selectedEntityId, setSelectedEntityId] = useState<string>('-1');
  console.log(deletingCollectionTemplateData);

  const [formConfiguration, setFormConfiguration] = useState<FormConfiguration>({
    form: {
      logo: {
        show_logo: true,
        logo_url: `${GoTrustTitle}`,
        width: '111px',
        height: '37px',
      },
      heading: {
        text: 'Consent Form',
        color: '#171717',
        size: '18px',
      },
      description: {
        text: 'By signing below, you confirm that you have read and understood the terms of this consent form and voluntarily agree to the processing of your personal data as described herein.',
        color: '#000000',
        size: '16px',
      },
      font_family: 'Poppins',
      color_scheme: '#2b6bec',
      border_radius: '10px',
      privacy_policy_url: '',
      show_privacy_policy_url: true,
      privacy_policy_url_text: 'Privacy Policy',
      show_language: true,
    },
    pii_section: {
      show_pii_section: false,
      show_labels: true,
      show_badges: false,
      heading: {
        text: '',
        color: '#000000',
        size: '20px',
      },
      description: {
        text: '',
        color: '#000000',
        size: '20px',
      },
    },
    consent_collection_section: {
      show_check_all_checkbox: true,
      all_checkbox_text:
        'I hereby give my explicit consent to GoTrust Pvt Ltd, for the processing of my personal data for the purposes mentioned below.',
      show_individual_checks: true,
      heading: {
        text: '',
        color: '#000000',
        size: '20px',
      },
      description: {
        text: '',
        color: '#000000',
        size: '16px',
      },
    },
    form_footer: {
      heading: {
        text: '',
        color: '#000000',
        size: '20px',
      },
      description: {
        text: `<p>By submitting this form, I agree to all <a target="_blank" rel="noopener noreferrer" href="https://www.gotrust.tech/terms-of-use"><u>terms and conditions</u></a>. Additionally, I consent to GoTrust collecting, using, and sharing my personal and sensitive information in accordance with <a target="_blank" rel="noopener noreferrer nofollow" href="https://www.gotrust.tech/Privacy-Policy"><u>GoTrust's Data Privacy Policy</u></a>. This policy upholds the highest standards of privacy and governance, as outlined on the website.</p><p></p><p>If you wish to withdraw your consent at any time, please <a target="_blank" rel="noopener noreferrer nofollow" href="https://sandbox.gotrust.tech/pf?customer_id=727&amp;pf_id=38"><u>click here to withdraw</u></a>.</p>`,
        color: '#000000',
        size: '16px',
      },
    },
    submit_button: {
      text: 'Submit',
      color: '#2b6bec',
      size: '16px',
    },
  });

  //! USE EFFECTS
  // fetching collection template data

  // fetching frequency
  // useEffect(() => {
  //   const fetchFrequency = async () => {
  //     try {
  //       // Call the returned function to fetch data
  //       const responseData = await get_frequency(customer_id);
  //       setFrequencyList(responseData?.result?.data);
  //     } catch (error) {
  //       console.error(error);
  //     }
  //   };

  //   fetchFrequency();
  // }, [customer_id]);

  // const {data : frequencyList} = useQuery<FrequencyProperties[]>({
  //   queryKey : ["FrequenctList" , customer_id],
  //   queryFn : async ()=>{
  //     const responseData = await get_frequency(customer_id);
  //     return (responseData?.result?.data);
  //   },
  //   enabled : !!customer_id,
  //   initialData : [],
  // })

  //! Handler Functions

  const {
    data: formData,
    isLoading,
    isError,
  } = useQuery<DataType>({
    queryKey: ['formData', sourceId],
    queryFn: async () => {
      const response = await httpClient.get<ResponseType>(
        `${import.meta.env.VITE_APP_GO_TRUST_UNIVERSAL_BASE_API}/api/v3/gtf/display?source_id=${sourceId}`
      );
      return response.data?.result?.data;
    },
    // staleTime: 1000 * 60 * 30,
    enabled: shouldFetchFormData && !!sourceId,
    onSuccess: (data) => {
      if (data?.form_configuration && Object.keys(data?.form_configuration)?.length !== 0) {
        setFormConfiguration(data?.form_configuration);
      } else if (data?.form_configuration && Object.keys(data?.form_configuration)?.length === 0) {
        setFormConfiguration(formConfiguration);
      }

      const initialPiiValues: PiiValuesType = {};

      data?.pii_list?.forEach((pii) => {
        initialPiiValues[pii.pii_label_id] = '';
      });
    },
  });

  const {
    data: preferenceCenterData,
    isLoading: isPreferenceCenterLoading,
    isError: isPreferenceCenterError,
  } = useQuery({
    queryKey: ['preferenceCenterData', preferenceCenterId],
    queryFn: async () => {
      const response = await get_preference_center_data(
        customer_id,
        preferenceCenterId || undefined
      );
      return response?.result?.data;
    },
    enabled: shouldFetchPreferenceData && !!preferenceCenterId && !!customer_id,
  });

  const { mutate: deleteCollectionTemplate } = useMutation({
    mutationFn: async (data: any) => {
      const response = await httpClient.put(
        UCM_REMOVE_COLLECTION_TEMPLATE,
        deletingCollectionTemplateData
      );
      return response;
    },
    onSuccess: (data) => {
      toast.dismiss();
      toast.success(t('ToastMessages.General.DeletedSuccessfully'));
      setReloadCollectionTemplate((prev) => !prev);
      setDeletingCollectionTemplateDialogOpen(false);
    },
    onError: (error) => {
      toast.dismiss();
      toast.error(t('Failed to delete'));
      setDeletingCollectionTemplateDialogOpen(false);
    },
  });

  const updateSearchTerm = useCallback(
    debounce((event: ChangeEvent<HTMLInputElement>) => {
      setSearchTerm(event.target.value);
      // fetchData(event.target.value); // call API to fetch data
    }, 500), // delay in ms
    []
  );

  const handleCreateCollectionTemplate = async () => {
    try {
      // Make the API request (replace `yourApiEndpoint` with the actual API endpoint)
      const response = await checkUnifiedPiiInCollectionTemplate(customer_id);
      dispatch(
        universalConsentManagementActions.setFormSourceId(response?.result?.data[0]?.source_id)
      );

      if (response.success) {
        navigate(`${UCM_ADD_COLLECTION_TEMPLATE}`);
        dispatch(universalConsentManagementActions.toInitialState());
        // setDisableSaveDraft(true);
      }
    } catch (error) {
      // eslint-disable-next-line unicorn/prefer-ternary
      if (axios.isAxiosError(error)) {
        // Axios specific error handling
        toast.dismiss(); // Clear any existing toasts
        // const status = error?.response?.data?.status_code;
        // const statusText = error?.response?.data?.message;
        const errorMessage = error?.response?.data?.result?.error || error.message;
        toast.error(`${errorMessage}`);
        console.error('Axios Error:', error);
      } else {
        // Generic error handling
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.UniversalConsentManagement.unexpectedErrorOccurred'));
        console.error('Unexpected Error:', error);
      }
    }
  };

  const handleRowClick = (data: CollectionTemplateData) => {
    dispatch(universalConsentManagementActions.setCollectionTemplateId(data?.id));
    dispatch(universalConsentManagementActions.setCurrentStep(6));
    navigate(UCM_ADD_COLLECTION_TEMPLATE);
    dispatch(universalConsentManagementActions.setPreferenceFormToInitialState());
    dispatch(universalConsentManagementActions.setPrivacyNoteToInitialState());
    dispatch(universalConsentManagementActions.setConsentFormToInitialState());
    dispatch(universalConsentManagementActions.setSections([]));
    // setSelectedCollectionData(data);
    // setShowModalView(true);
  };

  const handleChangeCollectionTemplateStatus = (
    event: ChangeEvent<HTMLInputElement>,
    collection_template_id: number
  ) => {
    event?.stopPropagation();
  };

  //! USE EFFECTS

  // fetch collection template table data
  // useEffect(() => {
  //   const fetchCollectionTemplate = async (customer_id: number, searchTerm: string | undefined) => {
  //     try {
  //       setIsLoading(true);
  //       const response = await fetchCollectionTemplateData(
  //         customer_id,
  //         filter,
  //         selectedEntityId,
  //         searchTerm
  //       );
  //       if (response.status !== 200) {
  //         throw new Error('Failed to fetch category data data.');
  //       }

  //       const consentData = response?.data?.result?.data || [];

  //       setCollectionTemplateList(consentData);
  //     } catch (error) {
  //       if (error instanceof Error) {
  //         console.error('Fetching processing category data failed!');
  //       } else {
  //         console.error('An unknown error occurred:', error);
  //       }
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   };

  //   if (customer_id) {
  //     fetchCollectionTemplate(customer_id, searchTerm);
  //   }
  // }, [reloadCollectionTemplate, selectedEntityId, searchTerm]);

  const { data: collectionTemplateList, isLoading: collectionTemplateListLoading } = useQuery({
    queryKey: ['collectionTemplateList', reloadCollectionTemplate, selectedEntityId, searchTerm],
    queryFn: async () => {
      const response = await fetchCollectionTemplateData(
        customer_id,
        filter,
        selectedEntityId,
        searchTerm
      );
      if (response.status !== 200) {
        throw new Error('Failed to fetch category data data.');
      }
      return response?.data?.result?.data || [];
    },
    enabled: !!customer_id,
    initialData: [],
  });

  // fetching entities
  // useEffect(() => {
  //   const fetchDomains = async () => {
  //     try {
  //       // Call the returned function to fetch data
  //       const responseData = await get_entities(customer_id);
  //       setEntities(responseData?.result?.rows);
  //       if (responseData?.result?.rows?.length === 1) {
  //         setSelectedEntityId(responseData?.result?.rows[0]?.id);
  //       }
  //     } catch (error) {
  //       console.error(error);
  //     }
  //   };

  //   fetchDomains();
  // }, [customer_id]);

  const { data: entities, isLoading: entitiesLoading } = useQuery<EntityProperties[]>({
    queryKey: ['entities', customer_id],
    queryFn: async () => {
      const response = await get_entities(customer_id);
      return response?.result?.rows;
    },
    enabled: !!customer_id,
  });

  return (
    <>
      <main className="flex size-full flex-col gap-6 font-primary-text">
        {/* Navbar */}
        <nav className="flex flex-row items-center justify-end gap-2">
          <div className="w-[192px]">
            <Select value={selectedEntityId} onValueChange={(value) => setSelectedEntityId(value)}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select option" />
              </SelectTrigger>
              <SelectContent>
                {entities?.length && entities?.length >= 2 && (
                  <SelectItem value="-1">All</SelectItem>
                )}
                {entities?.map((entity) => (
                  <SelectItem value={entity?.id?.toString()}>{entity?.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Input
            placeholder={t('Common.Search')}
            className="w-[240px]"
            secondIcon={searchIcon}
            onChange={updateSearchTerm}
          />
          <Button
            className="theme-hover-effect flex h-12 flex-row gap-2 text-primary-background"
            onClick={handleCreateCollectionTemplate}
          >
            <img src={addIcon} alt=""></img>
            {t('ucm.CollectionBuilder.CreateTemplate')}
          </Button>
        </nav>

        {/* Templates */}
        <section
          className="flex size-full flex-col items-start justify-start gap-6 rounded-xl px-[25px]"
          style={{ height: 'calc(100% - 80px)' }}
        >
          {/* Templates View */}
          {/* <section className="flex h-[150px] w-full flex-row flex-wrap gap-8">
          <div className="flex flex-col items-center gap-2">
            <img
              src={template}
              className="h-[113px] w-[126px] cursor-pointer rounded-xl border-2 border-primary-border hover:border-primary"
              alt="template"
            ></img>
            <p className="text-sm font-light text-black">Email Template</p>
          </div>

          <div className="flex flex-col items-center gap-2">
            <img
              src={template}
              className="h-[113px] w-[126px] cursor-pointer rounded-xl border-2 border-primary-border hover:border-primary"
              alt="template"
            ></img>
            <p className="text-sm font-light text-black">Form Template</p>
          </div>
        </section> */}

          {/* Templates Table */}
          <section className="inline-flex size-full flex-col items-start justify-start overflow-auto rounded-xl border border-slate-200 bg-white shadow">
            <div
              style={{ height: 'calc(100vh - 10rem)' }}
              className="table_main_content mt-0 w-full overflow-auto"
            >
              <DynamicTable<CollectionTemplateData>
                data={collectionTemplateList}
                loading={collectionTemplateListLoading}
                columns={columns}
                onRowClick={handleRowClick}
                enableSorting
                enablePagination
                searchTerm={searchTerm}
              />
            </div>
          </section>
          {/* <div className="flex w-full justify-end">
          <TablePaginationDemo
            count={count}
            handleItemsChange={handleNumberOfPages}
            handlePageChange={handlePageChange}
            currentPage={currentPage}
            numOfItems={numberOfItems}
          />
        </div> */}
        </section>
        {deletingCollectionTemplateDialogOpen && (
          <ShadcnDialog
            open={deletingCollectionTemplateDialogOpen}
            onOpenChange={setDeletingCollectionTemplateDialogOpen}
            title="Delete Collection Template"
            footer={
              <div className="flex w-full flex-row justify-end gap-2">
                <Button
                  variant="outline"
                  className="text-primary"
                  onClick={() => setDeletingCollectionTemplateDialogOpen(false)}
                >
                  No
                </Button>
                <Button
                  variant="default"
                  className="bg-custom-primary text-primary-background"
                  onClick={deleteCollectionTemplate}
                >
                  Yes
                </Button>
              </div>
            }
          >
            <p className="font-primary-text">{`Are you sure you want to delete this collection template`}</p>
          </ShadcnDialog>
        )}

        {openFormDialog && (
          <ShadcnDialog
            open={openFormDialog}
            onOpenChange={(open) => {
              setOpenFormDialog(open);
              if (!open) {
                setShouldFetchFormData(false);
              }
            }}
            title="View Form"
            footer={false}
            dialogContentClassName="h-[80vh] overflow-y-auto min-w-[60vw] "
          >
            <DynamicConsentForm formConfig={formConfiguration} formData={formData} />
          </ShadcnDialog>
        )}

        {openPreferenceDialog && (
          <ShadcnDialog
            open={openPreferenceDialog}
            onOpenChange={(open) => {
              setOpenPreferenceDialog(open);
              if (!open) {
                setShouldFetchPreferenceData(false);
              }
            }}
            title="View Preference Center"
            footer={false}
            dialogContentClassName="h-[80vh] overflow-y-auto min-w-[40vw] max-w-[80vw]"
          >
            {isPreferenceCenterLoading ? (
              <div className="flex items-center justify-center p-8">
                <p>Loading preference center data...</p>
              </div>
            ) : isPreferenceCenterError ? (
              <div className="flex items-center justify-center p-8">
                <p className="text-red-500">Error loading preference center data</p>
              </div>
            ) : (
              <PreferenceFormView preferenceCenterData={preferenceCenterData} />
            )}
          </ShadcnDialog>
        )}
      </main>
    </>
  );
};

export default Templates;
