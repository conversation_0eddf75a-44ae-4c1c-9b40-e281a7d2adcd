import { Monitor, TabletSmartphone } from 'lucide-react';
import { useEffect, useState } from 'react';

import { useDispatch, useSelector } from 'react-redux';
import { Button } from '../../../../../@/components/ui/Common/Elements/Button/ButtonSort';
import {
  <PERSON><PERSON>,
  TabsList,
  TabsTrigger,
} from '../../../../../@/components/ui/Common/Elements/Tabs/Tabs';
import { Switch } from '../../../../../@/components/ui/switch';
import logoDark from '../../../../../assets/GoTrustLogo.svg';
import GoTrustTitle from '../../../../../assets/gotrustTitle_light.svg';
import { universalConsentManagementActions } from '../../../../../redux/reducers/UniversalConsentManagement/universal-consent-management-slice';
import { RootState } from '../../../../../redux/store';
import {
  CTCPMapBucket,
  EntityProperties,
  PreferenceCenter,
  PreferenceFormData,
  UserInputFormConfiguration,
  VerifyInputFormConfiguration,
} from '../../../../../types/universal-consent-management';
import {
  fetch_collection_template,
  get_entities,
  get_preference_list,
} from '../../../../common/services/universal-consent-management';
import UCMPreferenceForm from '../../../../Customization/UCM-Preference-Form/ucm-preference-form';
import styles from '../ConsentBuilderSteps/AddCollectionTemplate.module.css';
import PreferenceFormPreviewMobile from '../ConsentBuilderSteps/preference-form-preview-mobile';
import UserInputScreen from '../ConsentBuilderSteps/PreferenceCenter/UserInput/user-input';
import VerifyInputScreen from '../ConsentBuilderSteps/PreferenceCenter/VerifyInput/verify-input';

interface TextConfig {
  text: string;
  color: string;
  size: string;
}

interface LogoConfig {
  show_logo: boolean;
  logo_url: string;
  width: string;
  height: string;
}

interface FormConfig {
  logo: LogoConfig;
  heading: TextConfig;
  description: TextConfig;
  font_family: string;
  color_scheme: string;
  border_radius: string;
  privacy_policy_url: string;
  show_privacy_policy_url: boolean;
  privacy_policy_url_text: string;
  show_language: boolean;
}

interface PiiSectionConfig {
  show_pii_section: boolean;
  show_labels: boolean;
  show_badges: boolean;
  heading: TextConfig;
  description: TextConfig;
}

interface ConsentCollectionSectionConfig {
  show_check_all_checkbox: boolean;
  all_checkbox_text: string;
  show_individual_checks: boolean;
  heading: TextConfig;
  description: TextConfig;
}

interface FormFooterConfig {
  heading: TextConfig;
  description: TextConfig;
}

interface SubmitButtonConfig {
  text: string;
  color: string;
  size: string;
}

interface ConsentPurposeConfiguration {
  form: FormConfig;
  pii_section: PiiSectionConfig;
  consent_collection_section: ConsentCollectionSectionConfig;
  form_footer: FormFooterConfig;
  submit_button: SubmitButtonConfig;
}

export interface PreferenceFormConfig {
  fontFamily: string;
  logoUrl: string;
  showLogo: boolean;
  showCookie: boolean;
  showConsent: boolean;
  showDSR: boolean;
  showConsentFlow: boolean;
  title: string;
  description: string;
  privacy_notice_heading: string;
  preference_center_heading: string;
  dsr_center_heading: string;
  consent_flow_heading: string;
  dsrURL: string;
  dpoEmail: string;
  dsrContent: string;
  consent_purpose_configuration: ConsentPurposeConfiguration;
}

interface PreferenceFormViewProps {
  preferenceCenterData?: any;
}

const PreferenceFormView: React.FC<PreferenceFormViewProps> = ({ preferenceCenterData }) => {
  const dispatch = useDispatch();
  //! VARIABLES
  const customer_id = useSelector(
    (state: RootState) => state.auth.login.login_details?.customer_id
  );
  const collection_template_id = useSelector(
    (state: RootState) => state?.UCM?.CollectionTemplateData?.id
  );
  const subject_identity_type_id = useSelector(
    (state: RootState) => state?.UCM?.CollectionTemplateData?.basicInfo?.subject_identity_type_id
  );
  const entityId = useSelector(
    (state: RootState) => state.UCM.CollectionTemplateData.basicInfo.entity_id
  );
  const preference_center_id = useSelector(
    (state: RootState) => state.UCM.CollectionTemplateData.preferenceForm.selected_preference_id
  );

  const defaultPreferenceFormConfig = {
    fontFamily: 'Poppins',
    logoUrl: logoDark,
    showLogo: true,
    showCookie: false,
    showConsent: true,
    showDSR: true,
    showConsentFlow: true,
    title: 'Preference Center',
    description:
      'Welcome to your Preference Center! Here, you can customize your shopping experience by managing your communication preferences, choosing how you receive updates—via email, SMS, or push notifications—and selecting the types of products and promotions you want to hear about. Your preferences help us deliver personalized recommendations and exclusive offers tailored just for you. You can update your settings anytime, ensuring you only receive content that matters to you while we protect your privacy.',
    privacy_notice_heading: 'Privacy Notice',
    preference_center_heading: 'Consent Preferences',
    dsr_center_heading: 'DSR',
    consent_flow_heading: 'Consent Flow',
    dsrURL: 'https://www.example.com/dsr',
    dpoEmail: '<EMAIL>',
    dsrContent: `
      <p>
        If you wish to exercise your rights as a data subject in respect of the above, you may
        contact GoTrust through the
        <a
          href="${import.meta.env.VITE_APP_FRONTEND_URL}/data-subject-rights/task-overview/create-request"
          target="_blank"
          rel="noopener noreferrer"
          class="text-blue-500 underline transition duration-200 ease-in-out hover:text-blue-700"
        >
          GoTrust’s dedicated portal
        </a>
        . For other questions about your account on the GoTrust Services (for example,
        questions about the availability of the services and login errors), please contact
        GoTrust, as applicable, at the address <i><EMAIL></i>
      </p>
      <p>
        Please only use this form for queries related to your personal data (Data Subject
        request). For support, technical assistance and other types of requests, please
        contact us through our
        <a
          href="${import.meta.env.VITE_APP_FRONTEND_URL}/data-subject-rights/task-overview/create-request"
          target="_blank"
          rel="noopener noreferrer"
          class="text-blue-500 transition duration-200 ease-in-out hover:text-blue-700"
        >
          contact form
        </a>
        . The information collected in this form is intended to enable the Data Protection
        Officer to respond to your Data Subject Request. This information will be kept and
        retained after the Data Subject Request has been treated for a period of (01) one year
        and then deleted. In case your request is related to deletion, please note that this
        deletion request will apply to all data collected by our company and all related
        Organizing Committees for our company's websites/applications where you have
        registered.
      </p>
    `,
    consent_purpose_configuration: {
      form: {
        logo: {
          show_logo: true,
          logo_url:
            'https://storage-dev.gotrust.tech/api/v1/buckets/gt-bucket/objects/download?prefix=uploads/1744879089544-25828146.png',
          width: '111px',
          height: '37px',
        },
        heading: {
          text: 'Consent Form',
          color: '#171717',
          size: '18px',
        },
        description: {
          text: 'By signing below, you confirm that you have read and understood the terms of this consent form and voluntarily agree to the processing of your personal data as described herein.',
          color: '#000000',
          size: '16px',
        },
        font_family: 'Poppins',
        color_scheme: '#2b6bec',
        border_radius: '10px',
        privacy_policy_url: '',
        show_privacy_policy_url: true,
        privacy_policy_url_text: 'Privacy Policy',
        show_language: true,
      },
      pii_section: {
        show_pii_section: false,
        show_labels: true,
        show_badges: false,
        heading: {
          text: '',
          color: '#000000',
          size: '20px',
        },
        description: {
          text: '',
          color: '#000000',
          size: '20px',
        },
      },
      consent_collection_section: {
        show_check_all_checkbox: true,
        all_checkbox_text:
          'I hereby give my explicit consent to GoTrust Pvt Ltd, for the processing of my personal data for the purposes mentioned below.',
        show_individual_checks: true,
        heading: {
          text: '',
          color: '#000000',
          size: '20px',
        },
        description: {
          text: '',
          color: '#000000',
          size: '16px',
        },
      },
      form_footer: {
        heading: {
          text: '',
          color: '#000000',
          size: '20px',
        },
        description: {
          text: 'To see how we use your data and personalize your experience please read our Privacy Policy. You can unsubscribe at any time.',
          color: '#000000',
          size: '16px',
        },
      },
      submit_button: {
        text: 'Submit',
        color: '#2b6bec',
        size: '16px',
      },
    },
  };

  //! States
  const [preferenceCenterList, setPreferenceCenterList] = useState<PreferenceCenter[]>([]);
  const [activeTab, setActiveTab] = useState<string>('0');
  const [activeCustomizeTab, setActiveCustomizeTab] = useState<string>('0');
  const [userInputFormConfiguration, setUserInputFormConfiguration] =
    useState<UserInputFormConfiguration>({
      logo: {
        show_logo: true,
        logo_url: `${GoTrustTitle}`,
        width: '111px',
        height: '37px',
      },
      heading: {
        text: 'Access Your Preferences',
        color: '#000',
        size: '18px',
      },
      description: {
        text: 'Log in with your email to personalize your settings and stay updated on what matters most.',
        color: '#000000',
        size: '16px',
      },
      submit_button: {
        text: 'Login',
        color: '#2b6bec',
        size: '16px',
      },
      border_radius: '10px',
      font_family: 'Poppins',
    });
  const [verifyInputFormConfiguration, setVerifyInputFormConfiguration] =
    useState<VerifyInputFormConfiguration>({
      logo: {
        show_logo: true,
        logo_url: `${GoTrustTitle}`,
        width: '111px',
        height: '37px',
      },
      heading: {
        text: 'Verify your email',
        color: '#000',
        size: '18px',
      },
      description: {
        before_email: 'Check your inbox for a 6-digit code at',
        after_email: 'Enter it here to confirm and access your preferences.',
        color: '#000000',
        size: '16px',
      },
      submit_button: {
        text: 'Verify',
        color: '#2b6bec',
        size: '16px',
      },
      secondary_button: {
        text: 'Resend',
        color: '#fff',
        size: '16px',
      },
      border_radius: '10px',
      font_family: 'Poppins',
      authentication: true,
    });
  const [preferenceFormConfig, setPreferenceFormConfig] = useState<PreferenceFormConfig>({
    ...defaultPreferenceFormConfig,
  });
  const [consentStatus, setConsentStatus] = useState<Map<string, CTCPMapBucket[]>>(new Map());
  const [entities, setEntities] = useState<EntityProperties[]>([]);
  const [preferenceFormData, setPreferenceFormData] = useState<PreferenceFormData>();
  const allConsents = Array.from(consentStatus.values()).flat();
  const allChecked =
    allConsents.length > 0 &&
    allConsents.every((consent) => consent.consent_status || consent.compulsory_consent);

  const fetchCurrentCollectionTemplateData = async () => {
    try {
      const responseData = await fetch_collection_template(customer_id, collection_template_id);

      // Only proceed if customer_id is defined
      if (customer_id) {
        const currentTemplateData = {
          customer_id, // Now guaranteed to be a number
          collection_template_id,
          collection_template_name: responseData?.result?.data?.name,
          data_principal_id: 0, // Changed from null to 0 to match the UcmPreferenceForm type
          purpose_list: responseData?.result?.data?.ct_cp_map,
        };

        setPreferenceFormData((prev) => {
          if (!prev)
            return {
              ucm_preference_form: [currentTemplateData],
            };

          return {
            ...prev,
            ucm_preference_form: [...(prev.ucm_preference_form || []), currentTemplateData],
          };
        });
      }
    } catch (error) {
      console.error(error);
    }
  };

  //! USE EFFECTS
  useEffect(() => {
    const fetchEntities = async () => {
      try {
        const responseData = await get_entities(customer_id);
        setEntities(responseData?.result?.rows);
      } catch (error) {
        console.error(error);
      }
    };
    fetchEntities();
  }, [customer_id]);

  // fetching preference centre list
  useEffect(() => {
    // This effect can be used for any preference center list related logic if needed
  }, [preferenceCenterList]);
  useEffect(() => {
    const fetchPreferenceList = async () => {
      try {
        // Call the returned function to fetch data
        const responseData = await get_preference_list(
          customer_id,
          entityId,
          subject_identity_type_id
        );
        setPreferenceCenterList(responseData?.data);
      } catch (error) {
        console.error(error);
      }
    };

    fetchPreferenceList();
  }, [customer_id]);

  // Process preference data from props
  useEffect(() => {
    if (preferenceCenterData) {
      dispatch(
        universalConsentManagementActions.setPreferenceTitle(
          preferenceCenterData.basic_info?.title || ''
        )
      );
      dispatch(
        universalConsentManagementActions.setPreferenceDescription(
          preferenceCenterData.basic_info?.description || ''
        )
      );

      const formData: PreferenceFormData = preferenceCenterData;

      if (formData?.privacy_note) {
        dispatch(universalConsentManagementActions.setSections(formData.privacy_note));
      }

      if (formData?.preference_center_configuration) {
        setUserInputFormConfiguration(
          formData?.preference_center_configuration?.user_input_configuration
        );
        setVerifyInputFormConfiguration(
          formData?.preference_center_configuration?.verify_input_configuration
        );
        setPreferenceFormConfig({
          fontFamily: formData?.preference_center_configuration?.fontFamily,
          logoUrl: formData?.preference_center_configuration?.logoUrl,
          showLogo: formData?.preference_center_configuration?.showLogo,
          showCookie: formData?.preference_center_configuration?.showCookie,
          showConsentFlow: formData?.preference_center_configuration?.showConsentFlow,
          showConsent: formData?.preference_center_configuration?.showConsent,
          showDSR: formData?.preference_center_configuration?.showDSR,
          title: formData?.preference_center_configuration?.title,
          description: formData?.preference_center_configuration?.description,
          privacy_notice_heading: formData?.preference_center_configuration?.privacy_notice_heading,
          preference_center_heading:
            formData?.preference_center_configuration?.preference_center_heading,
          dsr_center_heading: formData?.preference_center_configuration?.dsr_center_heading,
          consent_flow_heading: formData?.preference_center_configuration?.consent_flow_heading,
          dsrURL: formData?.preference_center_configuration?.dsrURL,
          dpoEmail: formData?.preference_center_configuration?.dpoEmail,
          dsrContent: formData?.preference_center_configuration?.dsrContent,
          consent_purpose_configuration:
            formData?.preference_center_configuration?.consent_purpose_configuration,
        });
        setPreferenceFormData(formData);
        // Populate consentStatus state based on response data
        formData?.ucm_preference_form?.length > 0 &&
          formData.ucm_preference_form.forEach((formItem) => {
            formItem?.purpose_list?.forEach((purpose) => {
              purpose.consent_bucket.forEach((consent) => {
                setConsentStatus((prev) => {
                  const updatedMap = new Map(prev);
                  updatedMap.set(
                    `${consent.consent_purpose_id}_${formItem?.collection_template_id}`,
                    consent?.ct_cp_map_bucket
                  );
                  return updatedMap;
                });
              });
            });
          });
      }
    }
    if (customer_id && collection_template_id) fetchCurrentCollectionTemplateData();
  }, [preferenceCenterData, customer_id, collection_template_id]);

  return (
    <>
      <main className="w-full rounded-lg border border-primary-border bg-primary-background font-primary-text">
        <section className={`flex size-full flex-col gap-4 p-7 ${styles.table_main_content}`}>
          <div className="flex h-fit w-full flex-row justify-between gap-4">
            <Tabs
              value={activeCustomizeTab}
              onValueChange={(currentTab) => setActiveCustomizeTab(currentTab)}
              className="h-12 w-1/3 min-w-[500px] flex-wrap items-center rounded-lg bg-[#f6f6f6] p-2"
            >
              <TabsList className="flex size-full flex-row items-center justify-between">
                {['User Input', 'Verify Input', 'Preference Center']?.map((item, index) => (
                  <Button variant="ghost" className="w-full p-0" key={item}>
                    <TabsTrigger
                      value={index.toString()}
                      className={`${activeCustomizeTab === index.toString() ? 'text-custom-primary' : ''} w-full`}
                    >
                      {item}
                    </TabsTrigger>
                  </Button>
                ))}
              </TabsList>
            </Tabs>
            <div className="flex w-full flex-row justify-end gap-2">
              <Monitor />
              <Switch
                checked={activeTab === '1'}
                onCheckedChange={(value: boolean) => setActiveTab(value ? '1' : '0')}
              />
              <TabletSmartphone />
            </div>
          </div>
          {activeCustomizeTab === '0' && (
            <section className="flex w-full flex-row justify-between">
              {/* <section className="h-auto w-2/5 p-2">
                <UCMPreferenceFormConfiguration
                  config={preferenceFormConfig}
                  defaultConfig={defaultPreferenceFormConfig}
                  setConfig={setPreferenceFormConfig}
                />
                <UserInputConfiguration
                  config={userInputFormConfiguration}
                  setConfig={setUserInputFormConfiguration}
                />
              </section> */}
              {activeTab === '0' ? (
                <UserInputScreen formConfig={userInputFormConfiguration} subject_identity="Email" />
              ) : (
                // <PreferenceFormPreview/>
                <PreferenceFormPreviewMobile
                  formConfig={userInputFormConfiguration}
                  subject_identity="Email"
                  entities={entities}
                  activeCustomizeTab={'userInput'}
                />
              )}
            </section>
          )}
          {activeCustomizeTab === '1' && (
            <section className="flex w-full flex-row justify-between">
              {/* <section className="h-auto w-2/5 p-2">
                <VerifyInputConfiguration
                  config={verifyInputFormConfiguration}
                  setConfig={setVerifyInputFormConfiguration}
                />
              </section> */}
              {activeTab === '0' ? (
                <VerifyInputScreen
                  formConfig={verifyInputFormConfiguration}
                  otp={['', '', '', '', '', '']}
                />
              ) : (
                // <PreferenceFormPreview/>
                <PreferenceFormPreviewMobile
                  activeCustomizeTab={'verifyInput'}
                  formConfig={verifyInputFormConfiguration}
                  otp={['', '', '', '', '', '']}
                />
              )}
            </section>
          )}
          {activeCustomizeTab === '2' && (
            <section className="flex w-full flex-row justify-between">
              {/* <section className="h-auto w-2/5 p-2">
                <UCMPreferenceFormConfiguration
                  config={preferenceFormConfig}
                  defaultConfig={defaultPreferenceFormConfig}
                  setConfig={setPreferenceFormConfig}
                />
              </section> */}
              {activeTab === '0' ? (
                <UCMPreferenceForm
                  customerId={customer_id}
                  pfId={preference_center_id}
                  setConfig={setPreferenceFormConfig}
                  config={preferenceFormConfig}
                  allChecked={allChecked}
                  allConsents={allConsents}
                  consentStatus={consentStatus}
                  preferenceFormData={preferenceFormData}
                  setConsentStatus={setConsentStatus}
                />
              ) : (
                <PreferenceFormPreviewMobile
                  activeCustomizeTab={'preferenceCenter'}
                  formConfig={preferenceFormConfig}
                  preferenceFormData={preferenceFormData}
                />
              )}
            </section>
          )}
        </section>
      </main>
    </>
  );
};

export default PreferenceFormView;
