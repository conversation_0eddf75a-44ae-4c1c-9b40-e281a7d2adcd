import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { SkeletonCard } from '../../../../@/components/ui/Common/Elements/Skeleton/Skeleton';
import greyCircle from '../../../../assets/GreyEllipse.svg';
import { fetchPolicyAuditLogData } from '../../../../redux/actions/CreatePolicyActions/CreatePolicyActions';
import { createPolicyActions } from '../../../../redux/reducers/CreatePolicy/CreatePolicySlice';
import { decryptId } from '../../../../utils/cipher';
import { getInitialsByName } from '../../../../utils/helperData';
import Modal from '../../../UI/Modal';
import AvatarFrame from '../../../common/Avatar';
import TablePaginationDemo from '../../../common/Pagenation';
import { FETCH_POLICY_AUDIT_LOG } from '../../../common/api';
import CommentModal from './CommentModal/Comment';

export default function AuditLog() {
  const dispatch = useDispatch();
  const count = useSelector((state: any) => state.createPolicy.policyAuditLog.count);
  const auditLogData = useSelector((state: any) => state.createPolicy.policyAuditLog.rows);
  const numberOfItems = useSelector((state: any) => state.createPolicy.policyAuditLog.itemsPerPage);
  const currentPage = useSelector((state: any) => state.createPolicy.policyAuditLog.currentPage);
  const createPolicyId = useSelector((state: any) => state.policyManagement.createdPolicyId);

  const [isLoading, setIsLoading] = useState(true);
  const [currentComment, setCurrentComment] = useState('');
  const [currentUserType, setCurrentUserType] = useState('');
  const [currentCommentBy, setCurrentCommentBy] = useState('');
  const [modalIsOpen, setModalIsOpen] = useState<boolean>(false);

  function handleNumberOfPages(value: number) {
    dispatch(createPolicyActions.setAuditLogItemsPerPage(value));
  }

  function handlePageChange(value: number) {
    dispatch(createPolicyActions.setAuditLogCurrentPage(value));
  }

  function handleOpen(comment: string, commentBy: string, action: string) {
    const type = action.split(' ');
    setCurrentUserType(action.split(' ')[type.length - 1]);
    setCurrentCommentBy(commentBy);
    setCurrentComment(comment);
    setModalIsOpen(true);
  }

  useEffect(() => {
    setIsLoading(true);
    const fetchData = async () => {
      const decipherId = decryptId(createPolicyId);

      const url = `${FETCH_POLICY_AUDIT_LOG}/${decipherId}?page=${currentPage}&size=${numberOfItems}`;

      dispatch(createPolicyActions.setAuditLogData({ count: 0, rows: [] }));

      try {
        const resData = await dispatch<any>(fetchPolicyAuditLogData(url));

        const auditLogList = resData.result;

        dispatch(createPolicyActions.setAuditLogData(auditLogList || { count: 0, rows: [] }));
        setIsLoading(false);
      } catch (error) {
        if (error instanceof Error) {
          // console.log("Fetching Audit Log data failed!");
        } else {
          // console.log("An unknown error occurred:", error);
          // Handle other types of errors if needed
        }
        setIsLoading(false);
      }
    };
    fetchData();
  }, [numberOfItems, currentPage]);

  return (
    <div style={{ height: 'calc(100vh - 21rem)' }}>
      {modalIsOpen && (
        <Modal
          open={modalIsOpen}
          onClose={() => setModalIsOpen(false)}
          cssClass="w-[31.7%] shrink-0 rounded-2xl bg-white shadow-[10px_20px_30px_0px_rgba(0,0,0,0.10)]"
        >
          <CommentModal
            type={currentUserType}
            comment={currentComment}
            commentBy={currentCommentBy}
            onClose={() => setModalIsOpen(false)}
          />
        </Modal>
      )}
      <div className="flex size-full shrink-0 flex-col gap-3 overflow-auto scrollbar-hide">
        {/* {isLoading && auditLogData.length === 0 && ( */}
        {/* // <div className="flex flex-row gap-3 items-center justify-center w-full"> */}
        {/* <img src={loader} alt="Loader" className="h-8 w-8 justify-center items-center"/> */}
        {/* //   <svg */}
        {/* //     aria-hidden="true"
          //     className="w-6 h-6 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
          //     viewBox="0 0 100 101"
          //     fill="none"
          //     xmlns="http://www.w3.org/2000/svg"
          //   > */}
        {/* //     <path */}
        {/* //       d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
          //       fill="currentColor"
          //     /> */}
        {/* //     <path */}
        {/* //       d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
          //       fill="currentFill"
          //     /> */}
        {/* //   </svg>
          // </div> */}
        {/* // )} */}
        {!isLoading && auditLogData.length === 0 && (
          <div className="flex w-full flex-row items-center justify-center font-[Poppins]">
            Data Not Found!
          </div>
        )}
        {isLoading && auditLogData.length === 0 ? (
          <>
            <SkeletonCard />
            <SkeletonCard />
            <SkeletonCard />
            <SkeletonCard />
          </>
        ) : (
          auditLogData.map((item: any, index: number) => (
            <div
              key={`${item.name}_${index + 1}`}
              className="flex w-full flex-row items-center gap-3"
            >
              {/* <svg
                width="42"
                height="40"
                viewBox="0 0 42 40"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <>
                  <circle
                    id="Ellipse 1390"
                    cx="20"
                    cy="20.4922"
                    r="20"
                    fill="#FF7D6C"
                    // fill={generateDonutChartColors(10)[Math.floor(Math.random() * 10)]}
                  />
                  <foreignObject y="8" x="0.5" width="40" height="41">
                    <div className="text-black text-center text-base font-normal leading-[24px] justify-center tracking-[0.15px] w-full">
                      <p>{item.initials}</p>
                    </div>
                  </foreignObject>
                </>
              </svg> */}
              <div
                onClick={() =>
                  item?.isComment
                    ? handleOpen(item?.comment, item?.name, item?.action)
                    : setModalIsOpen(false)
                }
                className={`flex flex-row px-[6px] py-[7px] lg:px-[13px] ${
                  item?.isComment ? 'cursor-pointer' : 'cursor-default'
                } w-full items-center justify-between rounded-lg border border-[#C0CDE0] bg-white text-base leading-normal text-[#999]`}
              >
                <div className="flex w-full flex-row">
                  <div className="flex w-[25rem] items-center gap-1 p-2.5 lg:gap-2.5">
                    <AvatarFrame value={item?.name} getInitials={getInitialsByName} />
                    {item.name}
                  </div>
                  <div className="flex w-[50rem] items-center gap-1 p-2.5 lg:gap-2.5">
                    <p className="font-medium">{item.action}</p>
                  </div>
                  <div className="flex w-60 items-center gap-1 p-2.5 lg:gap-2.5">
                    <img src={greyCircle} alt="Grey Circle Dot" />
                    {item.createdOnDate}
                  </div>
                  <div className="flex items-center gap-2.5 whitespace-nowrap">
                    <img src={greyCircle} alt="Grey Circle Dot" />
                    {new Date(item?.createdAt).toLocaleTimeString('en-US', {
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </div>
                  {/* <div className="flex p-2.5 items-center lg:gap-2.5 gap-1">
                    <img src={greyCircle} alt="Grey Circle Dot" />
                    {item.createdOnTime}
                  </div> */}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      <TablePaginationDemo
        count={count}
        handleItemsChange={handleNumberOfPages}
        handlePageChange={handlePageChange}
        currentPage={currentPage}
        numOfItems={numberOfItems}
      />
    </div>
  );
}
