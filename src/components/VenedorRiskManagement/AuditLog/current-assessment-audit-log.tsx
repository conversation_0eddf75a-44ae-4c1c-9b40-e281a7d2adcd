import { Download } from 'lucide-react';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { Button } from '../../../@/components/ui/Common/Elements/Button/ButtonSort';
import { Dialog, DialogContent } from '../../../@/components/ui/Common/Elements/Dialog/Dialog';
import httpClient from '../../../api/httpClient';
import download from '../../../assets/downloadedTransition.svg';
import greyCircle from '../../../assets/GreyEllipse.svg';
import { Vrm_Internal_Audit_Log } from '../../../redux/actions/VRM/vrm-taskoverview-actions';
import {
  setCurrentAssessmentAuditLogData,
  setVrmAssessmentListAuditLogPaginationCount,
  setVrmAssessmentListAuditLogPaginationItemsPerPage,
  setVrmAssessmentListAuditLogPaginationPage,
} from '../../../redux/reducers/VRM/assessment-taskoverview-slice';
import { RootState } from '../../../redux/store';
import { CurrentAssessmentAuditLogData } from '../../../redux/types/vendor-risk-management';
import { COMMON_VRM_ASSESSMENT_ENDPOINT } from '../../common/api';
import AvatarFrame from '../../common/Avatar';
import { convertDateToHumanView } from '../../common/CommonHelperFunctions';
import TablePaginationDemo from '../../common/Pagenation';

interface AuditLog {
  searchValue: string;
}

const CurrentAssessmentAuditLog: React.FC<AuditLog> = ({ searchValue }) => {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState<boolean>(false);
  const isLoading = useSelector((state: RootState) => state.vrmAssessmentTaskOverview?.loading);
  const count = useSelector(
    (state: RootState) => state.vrmAssessmentTaskOverview?.pagination.count
  );
  const currentPage = useSelector(
    (state: RootState) => state.vrmAssessmentTaskOverview?.pagination.page
  );
  const numberOfItems = useSelector(
    (state: RootState) => state.vrmAssessmentTaskOverview?.pagination.itemsPerPage
  );
  const auditLogData = useSelector(
    (state: RootState) => state.vrmAssessmentTaskOverview?.currentAssessmentAuditLogData
  );
  const currentRiskAssessmentKey = useSelector(
    (state: RootState) => state?.riskAssessment?.selectedRowData?.Assessment?.key
  );
  const id = useSelector((state: any) => state?.riskAssessment?.selectedRowData?.id);

  function handleNumberOfPages(value: number) {
    dispatch(setVrmAssessmentListAuditLogPaginationItemsPerPage(value));
  }

  function handlePageChange(value: number) {
    dispatch(setVrmAssessmentListAuditLogPaginationPage(value));
  }

  const fetchAuditLog = async () => {
    dispatch(setCurrentAssessmentAuditLogData([]));

    console.log('first');

    const response = await dispatch<any>(
      Vrm_Internal_Audit_Log(currentPage, numberOfItems, searchValue, currentRiskAssessmentKey, id)
    );

    console.log('sec');
    if (response) {
      const auditData = response?.data?.result;

      console.log('third');

      dispatch(setCurrentAssessmentAuditLogData(auditData?.rows || []));
      dispatch(setVrmAssessmentListAuditLogPaginationCount(auditData?.count || 0));
    }
  };

  useEffect(() => {
    fetchAuditLog();
  }, [currentPage, numberOfItems, searchValue, dispatch]);

  const handleDownloadAuditLog = async () => {
    const request_id = id;
    const newCurrentAssessmentKey = currentRiskAssessmentKey.toUpperCase();
    try {
      toast.loading(t('FrontEndErrorMessage.VRM.ProcessingIn'));
      const response = await httpClient.get(
        `${COMMON_VRM_ASSESSMENT_ENDPOINT}/download-audit-log/${request_id}?type=${newCurrentAssessmentKey}`,
        {
          responseType: 'blob',
        }
      );

      if (response?.status) {
        toast.dismiss();
        setIsSuccessModalOpen(true);
      }
    } catch (error) {
      console.error('Unexpected error: ', error);
      throw error;
    }
  };

  return (
    <div style={{ height: `calc(100vh - 14rem)` }}>
      <div className="flex h-[90%] w-full shrink-0 flex-col gap-3 overflow-auto scrollbar-hide">
        <div className="flex justify-end p-2">
          <Button variant="outline" className="w-46 h-10" onClick={() => handleDownloadAuditLog()}>
            Downlaod
            <Download className="ml-2 h-4 w-4" />
          </Button>
        </div>
        <div>
          {isSuccessModalOpen && (
            <Dialog open={isSuccessModalOpen} onOpenChange={setIsSuccessModalOpen}>
              <DialogContent className="max-w-sm p-5">
                <div className="flex w-full flex-row items-center justify-end">
                  {/* <Button
                                    size="icon"
                                    variant="ghost"
                                    className="bg-[#E3E3E3] size-6 p-0 hover:bg-[#d1d1d1]"
                                    onClick={() => setIsSuccessModalOpen(false)}
                                  >
                                    <X className="h-4 w-4 text-black" />
                                  </Button> */}
                </div>

                <div className="flex flex-col items-center justify-center gap-3">
                  <img src={download} alt="download" className="aspect-square w-18" />
                  <div className="flex flex-col items-center justify-center gap-2.5">
                    <p className="text-center font-primary-text text-3xl font-semibold leading-normal text-quaternary-text">
                      Downloaded
                    </p>
                    <p className="text-center font-primary-text text-xs font-normal leading-normal text-[#686868]">
                      The download link has been sent to your email address. Please check your
                      inbox.
                    </p>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </div>
        {isLoading && auditLogData.length === 0 && (
          <div className="flex w-full flex-row items-center justify-center gap-3 pt-8">
            <svg
              aria-hidden="true"
              className="size-6 animate-spin fill-blue-600 text-gray-200 dark:text-gray-600"
              viewBox="0 0 100 101"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                fill="currentColor"
              />
              <path
                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                fill="currentFill"
              />
            </svg>
          </div>
        )}
        {!isLoading && auditLogData.length === 0 && (
          <div className="flex w-full flex-row items-center justify-center pt-8 font-[Poppins]">
            Data Not Found!!
          </div>
        )}

        {auditLogData.map((item: CurrentAssessmentAuditLogData, index: number) => (
          <div
            key={item.id}
            className="mb-3 flex w-full items-center justify-between rounded-lg border border-[#C0CDE0] bg-white px-[6px] py-[7px] text-base leading-normal text-[#999] lg:px-[13px]"
          >
            <div className="flex items-center gap-1">
              <AvatarFrame value={item?.initials} />
              <div className="flex flex-col">
                <span className="font-medium">{item?.name}</span>
              </div>
            </div>

            <div className="flex-1">
              <p className="pl-14 pr-10 font-medium">{item?.action}</p>
            </div>

            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2.5 whitespace-nowrap">
                <img src={greyCircle} alt="Grey Circle Dot" />
                {convertDateToHumanView(item?.createdAt)}
              </div>
              <div className="flex items-center gap-2.5 whitespace-nowrap">
                <img src={greyCircle} alt="Grey Circle Dot" />
                {new Date(item?.createdAt).toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </div>
            </div>
          </div>
        ))}
      </div>

      <TablePaginationDemo
        count={count}
        handleItemsChange={handleNumberOfPages}
        handlePageChange={handlePageChange}
        currentPage={currentPage}
        numOfItems={numberOfItems}
      />
    </div>
  );
};

export default CurrentAssessmentAuditLog;
