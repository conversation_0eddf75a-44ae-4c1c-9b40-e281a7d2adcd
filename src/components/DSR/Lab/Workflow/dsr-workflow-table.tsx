import { ColumnDef } from '@tanstack/react-table';
import { debounce } from 'lodash';
import { ArrowUpDown } from 'lucide-react';
import { ChangeEvent, useCallback, useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  addWorkflow,
  fetchWorkflowList,
  fetchWorkflowListById,
} from '../../../../../src/components/common/services/data-subject-request';
import { Button } from '../../../../@/components/ui/Common/Elements/Button/Button';
import { ImageButton } from '../../../../@/components/ui/Common/Elements/Button/ImageButton';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../../../../@/components/ui/Common/Elements/Dialog/Dialog';
import { Label } from '../../../../@/components/ui/Common/Elements/Label/Label';
import { Badge } from '../../../../@/components/ui/badge';
import solidEye from '../../../../assets/IconoirEyeSolid.svg';
import search from '../../../../assets/Search.svg';
import edit from '../../../../assets/images/edit_new.png';
import addSign from '../../../../assets/plusSign.svg';
import { setDsrWorkflowStep } from '../../../../redux/reducers/DSR/dsr-slice';
import { getInitialsByName } from '../../../../utils/helperData';
import { DSR_EDIT_WORKFLOW, DSR_VIEW_WORKFLOW } from '../../../../utils/routeConstant';
import AvatarFrame from '../../../common/Avatar';
import { convertDateToHumanView } from '../../../common/CommonHelperFunctions';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../../../../@/components/ui/Common/Elements/Form/Form';
import { Input } from '../../../../@/components/ui/Common/Elements/Input/Input';

import { useQuery } from '@tanstack/react-query';
import { Tooltip } from 'react-tooltip';
import SearchableSelect from '../../../../@/components/ui/Common/Elements/Select/SearchableSelect';
import httpClient from '../../../../api/httpClientNew';
import { fetchEntities } from '../../../../redux/actions/ActivityLog/ActivityLogActions';
import { getRegulationsList } from '../../../../redux/actions/CustomerManagement/CustomerManagementActions';
import { AppDispatch, RootState } from '../../../../redux/store';
import { DsrWorkflowTableItem, DsrWorkflowTableProp } from '../../../../types/data-subject-rights';
import { api_key_work as api_key, go_trust_base_api } from '../../../../utils/helperData';
import DynamicTable from '../../../common/ShadcnDynamicTable/dynamic-table';

const formSchema = z.object({
  flowtype: z
    .string()
    .min(1, 'This field is required')
    .regex(
      /^[A-Za-z][A-Za-z0-9_()-]*[\sA-Za-z0-9_\()-]*$/,
      'Must start with a letter and can include letters, parentheses, numbers, spaces, hyphens, and underscores'
    ),
  group_id: z.string().min(1, 'This field is required'),
  // regulation_id: z.array(z.number()).optional(),
});
interface PolicyRetentionResponse {
  count: number;
  rows: any[]; // API response structure
}

const DsrWorkflowTable = () => {
  const dispatch = useDispatch<AppDispatch>();
  const loginData = useSelector((state: RootState) => state.auth.login.login_details);
  const navigate = useNavigate();
  const [searchValue, setSearchValue] = useState('');
  const [workflowListData, setWorkflowListData] = useState<DsrWorkflowTableItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newWorkflowName, setNewWorkflowName] = useState('');
  const [flowTypeSuggestions, setFlowTypeSuggestions] = useState<string[]>([]);
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [retentionTypeFilter, setRetentionTypeFilter] = useState('default');
  const [categoryFilter, setCategoryFilter] = useState('default');
  const { t } = useTranslation();
  const { entities, selectedEntityId } = useSelector((state: RootState) => state.activityLog);
  const { sidebar } = useSelector((state: RootState) => state.SideBar);
  const [customerRegulationsList, setCustomerRegulationsList] = useState<any[]>([]);
  // API fetch function
  const fetchPolicyRetentionData = async () => {
    let url = `${go_trust_base_api}/api/v1/policy?retention=true&size=100&page=1`;

    if (searchTerm) {
      url += `&search=${searchTerm}`;
    }

    if (retentionTypeFilter !== 'default') {
      url += `&retention_policy_type=${retentionTypeFilter}`;
    }

    if (categoryFilter !== 'default') {
      url += `&category=${categoryFilter}`;
    }

    const response = await httpClient.get(url);
    return response?.data?.result;
  };
  // Use React Query for data fetching
  const { data: responseData, isLoading: retentionError } = useQuery<PolicyRetentionResponse>({
    queryKey: [
      'policy-retention',
      searchTerm,
      retentionTypeFilter,
      categoryFilter,
      api_key,
      // access_token,
    ],
    queryFn: fetchPolicyRetentionData,
    refetchOnWindowFocus: false,
  });
  //   const count = useSelector(
  //   (state: RootState) => state.dataSubjectRights?.workflowPagination.count
  // );
  // const currentPage = useSelector(
  //   (state: RootState) => state.dataSubjectRights?.workflowPagination.page
  // );
  // const numberOfItems = useSelector(
  //   (state: RootState) => state.dataSubjectRights?.workflowPagination.itemsPerPage
  // );

  // function handleNumberOfPages(value: number) {
  //   dispatch(setDsrWorkflowPaginationItemsPerPage(value));
  // }

  // function handlePageChange(value: number) {
  //   dispatch(setDsrWorkflowPaginationPage(value));
  // }

  const form = useForm<DsrWorkflowTableProp>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      flowtype: '',
      group_id: '',
      data_retention_rule_id: undefined,
      // regulation_id: []
    },
  });

  const customer_id = loginData?.customer_id ?? 0;

  const fetchData = async (searchTerm?: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetchWorkflowList(page, size, searchTerm);
      if (response?.data.success) {
        setWorkflowListData(response?.data.result.rows);
        setTotalCount(response?.data.result.count);
      } else {
        setError('Failed to fetch workflow list');
      }
    } catch {
      toast.error(t('FrontEndErrorMessage.ApiErrors.AnErrorOccurred'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData(searchValue);
  }, [page, size, searchValue]);

  useEffect(() => {
    dispatch(fetchEntities(customer_id, selectedEntityId));
  }, [dispatch, customer_id, selectedEntityId]);

  useEffect(() => {
    async function fetchRegulationList() {
      try {
        const data = await getRegulationsList();
        if (data && Array.isArray(data.rows)) {
          setCustomerRegulationsList(data.rows);
        } else {
          console.error('Expected "rows" to be an array in the API response.');
        }
      } catch (error) {
        console.error('Error fetching customer regulations list:', error);
      }
    }
    fetchRegulationList();
  }, []);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newSize: number) => {
    setSize(newSize);
    setPage(1);
  };

  const filterFlowTypes = useCallback(
    debounce((input: string) => {
      if (!input) {
        setFlowTypeSuggestions([]);
        return;
      }

      const filteredSuggestions = workflowListData
        .map((workflow) => workflow.flowtype)
        .filter((flowtype) => flowtype.toLowerCase().includes(input.toLowerCase()))
        .filter((value, index, self) => self.indexOf(value) === index);

      setFlowTypeSuggestions(filteredSuggestions);
    }, 300),
    [workflowListData]
  );

  // Modal Submit function
  const [isLoading, setIsLoading] = useState(false);

  const handleAddWorkflow = async () => {
    const newWorkflowName = form.getValues('flowtype');
    const selectedGroupId = form.getValues('group_id');
    if (!newWorkflowName.trim()) {
      toast.error(t('FrontEndErrorMessage.DSR.PleaseEnterWorkflowType'));
      return;
    }

    const existingFlowType = workflowListData.some(
      (workflow) =>
        workflow.flowtype.toLowerCase() === newWorkflowName.toLowerCase() &&
        String(workflow.group_id) === String(selectedGroupId)
    );

    if (existingFlowType) {
      toast.error(t('FrontEndErrorMessage.DSR.DuplicateWorkflow'));
      return;
    }

    setIsLoading(true); // Disable the button
    toast.loading(t('FrontEndErrorMessage.DSR.AddingWorkflow'));

    try {
      const response = await addWorkflow(newWorkflowName, selectedGroupId);
      if (response.success) {
        toast.dismiss();
        toast.success(t('FrontEndErrorMessage.DSR.WorkflowAddedSuccessfully'));
        setIsModalOpen(false);
        fetchWorkflowDetailById(response.result?.id, 'draft', 'edit');
      }
    } catch (error) {
      toast.dismiss();
      toast.error(t('FrontEndErrorMessage.DSR.FailedToAddWorkflow'));
    } finally {
      setIsLoading(false); // Enable the button again
    }
  };
  // Get workflow by id
  const fetchWorkflowDetailById = async (
    id: number,
    workflow_status?: string,
    eventType?: string,
    group_id?: string
  ) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetchWorkflowListById(id);
      if (response?.data.success) {
        dispatch(setDsrWorkflowStep(response?.data.result));
        if (eventType == 'edit')
          navigate(DSR_EDIT_WORKFLOW, {
            state: {
              id: id,
              workFlow: workflow_status,
              group_id: group_id || response?.data.result.group_id,
            },
          });
        else
          navigate(DSR_VIEW_WORKFLOW, {
            state: {
              name: response?.data.result.flowtype,
              group_id: group_id || response?.data.result.group_id,
            },
          });
      } else {
        setError('Failed to fetch workflow list');
      }
    } catch {
      toast.error(t('FrontEndErrorMessage.ApiErrors.AnErrorOccurred'));
    } finally {
      setLoading(false);
    }
  };

  //////////////////////
  // Update workflow
  const handleUpdateRequest = async (
    id: number,
    type: string,
    workflow_status: string,
    eventType: string,
    group_id: string
  ) => {
    fetchWorkflowDetailById(id, workflow_status, eventType, group_id);
  };

  const handleSearchValue = useCallback(
    debounce((event: ChangeEvent<HTMLInputElement>) => {
      setSearchValue(event.target.value);
      setPage(1);
    }, 1000),
    []
  );

  const workflowTableColumns: ColumnDef<DsrWorkflowTableItem>[] = [
    {
      accessorKey: 'flowtype',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('DSR.AssigneeModal.FlowType')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => <div>{row.getValue('flowtype') || '-'}</div>,
    },
    {
      accessorKey: 'created_by',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('RoleManagement.ViewRole.CreatedBy')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const fullName = `${row.original.firstName} ${row.original.lastName}`;
        return (
          <div className="flex flex-row items-center gap-2 whitespace-nowrap">
            <AvatarFrame value={fullName} getInitials={getInitialsByName} />
            {fullName}
          </div>
        );
      },
    },
    {
      accessorKey: 'business_unit',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Business Unit
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const group = row.original.Group;
        return <div>{group ? `${group.name}` : '-'}</div>;
      },
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('UserManagement.ViewUser.CreatedDate')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => <div>{convertDateToHumanView(row.getValue('createdAt'))}</div>,
    },
    {
      accessorKey: 'workflow_status',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('Ropa.Activity.TableHeading.Status')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const status = row.getValue('workflow_status') as string;
        const bgColor =
          status === 'draft' ? '#CBD5E1' : status === 'published' ? '#2DB078' : 'transparent';
        const textColor = status === 'published' ? '#ffffff' : 'inherit';

        return (
          <Badge
            className="justify-center text-center"
            style={{
              backgroundColor: bgColor,
              color: textColor,
              textTransform: 'capitalize',
            }}
          >
            {status || '-'}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'action',
      header: () => (
        <Button variant="ghost" className="p-0">
          {t('Ropa.Activity.TableHeading.Action')}
        </Button>
      ),
      cell: ({ row }) => (
        <div className="flex flex-row gap-2">
          <Button
            id="view"
            variant="default"
            className="w-8 p-0"
            onClick={() =>
              // navigate(DSR_VIEW_WORKFLOW, {
              //   state: { name: row.original.flowtype },
              // })
              handleUpdateRequest(
                row?.original.id,
                row.original.flowtype,
                row.getValue('workflow_status'),
                'view',
                row.original.group_id
              )
            }
          >
            {/* <img src={review} alt="View" className="aspect-square h-6" /> */}
            <img className="size-5" src={solidEye} alt="eye icon" />
            <Tooltip
              anchorSelect={`#view`}
              place="left"
              style={{
                color: '#FFF',
                fontFamily: 'Poppins',
                fontSize: '14px',
                fontStyle: 'normal',
                fontWeight: '400',
                lineHeight: 'normal',
                background: '#0B101C',
                borderRadius: '8px',
                opacity: '0.8',
                display: 'inline-flex',
                padding: '9px 16px',
                justifyContent: 'flex-start',
                alignItems: 'center',
                gap: '10px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
              }}
              content={'View'}
            />
          </Button>
          <Button
            id="edit"
            variant="default"
            className="w-8 p-0"
            onClick={
              () =>
                handleUpdateRequest(
                  row?.original.id,
                  row.original.flowtype,
                  row.getValue('workflow_status'),
                  'edit',
                  row.original.group_id
                )
              // navigate(DSR_EDIT_WORKFLOW, {
              //   state: { id: row.original.id, name: row.original.flowtype },
              // })
            }
          >
            <img src={edit} alt="Edit" className="aspect-square h-6" />
            <Tooltip
              anchorSelect={`#edit`}
              place="right"
              style={{
                color: '#FFF',
                fontFamily: 'Poppins',
                fontSize: '14px',
                fontStyle: 'normal',
                fontWeight: '400',
                lineHeight: 'normal',
                background: '#0B101C',
                borderRadius: '8px',
                opacity: '0.8',
                display: 'inline-flex',
                padding: '9px 16px',
                justifyContent: 'flex-start',
                alignItems: 'center',
                gap: '10px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
              }}
              content={'Edit'}
            />
          </Button>
        </div>
      ),
    },
  ];

  const openModal = () => {
    setNewWorkflowName('');
    setSearchValue('');
    setIsModalOpen(true);
  };

  useEffect(() => {
    if (!isModalOpen) {
      form.reset();
    }
  }, [isModalOpen]);

  return (
    <div className="size-full">
      <div className="w-full rounded-md bg-white font-[Poppins]">
        <div className="items-flex-end flex flex-col gap-4 p-5">
          <div className="flex h-9 flex-row items-center justify-between gap-5">
            <div>
              <Label className="text-xl font-semibold">{t('DSR.WorkFlow.WorkFlow')}</Label>
            </div>
            <div className="flex flex-row items-center gap-5">
              <div className="flex h-11 w-[222px] items-center justify-between rounded-md border border-solid border-input px-3">
                <img src={search} alt="search sign" className="size-[19px]" />
                <input
                  type="text"
                  placeholder={t('Common.Search')}
                  className="ml-1 h-auto w-full outline-none"
                  onChange={handleSearchValue}
                />
              </div>
              <ImageButton
                className="h-11 bg-custom-primary hover:bg-custom-primary"
                onClick={openModal}
              >
                <img src={addSign} alt="plussign" />
                <p className="text-primary-background"> {t('DSR.WorkFlow.AddWorkflow')}</p>
              </ImageButton>
            </div>
          </div>
        </div>
        <div></div>
      </div>
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('DSR.WorkFlow.AddWorkflow')}</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleAddWorkflow)}>
              <FormField
                control={form.control}
                name="flowtype"
                render={({ field }) => (
                  <FormItem className="pb-2">
                    <FormLabel>
                      <span>Enter workflow name</span>
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder={t('DSR.WorkFlow.EnterFlowType')} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="group_id"
                render={({ field }) => (
                  <FormItem className="pb-2">
                    <FormLabel>
                      Business Unit
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <SearchableSelect
                        placeholder="Select business unit"
                        options={
                          entities?.map((entity) => ({ value: entity?.name, id: entity?.id })) || []
                        }
                        value={field.value ? String(field.value) : ''}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {/* <div className="flex w-full flex-row items-center justify-between p-2">
                <p className="text-sm text-[#64748B]">Are you want to tag rentention policy?</p>
                <Switch id="targeting-cookies" onClick={(e) => e.stopPropagation()} />
              </div> 
              <FormField
                control={form.control}
                name="data_retention_rule_id"
                render={({ field }) => (
                  <FormItem className="pb-2">
                    <FormLabel>
                      Rentention Policy
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <SearchableSelect
                        placeholder="Select retention policy"
                        options={
                          responseData?.rows?.map((row) => ({ value: row?.name, id: row?.id })) || []
                        }
                        value={field.value ? String(field.value) : ''}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              /> */}
              <DialogFooter className="mt-4 flex justify-end">
                <Button type="button" variant="outline" onClick={() => setIsModalOpen(false)}>
                  {t('Common.Cancel')}
                </Button>
                <Button
                  type="submit"
                  className="bg-custom-primary text-white hover:bg-custom-primary"
                >
                  {t('Common.Add')}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      <div className="bottom-0 bg-white">
        {/* <TablePaginationDemo
          count={totalCount}
          currentPage={page}
          numOfItems={size}
          handlePageChange={handlePageChange}
          handleItemsChange={handlePageChange}
        /> */}
        <div
          style={{ height: 'calc(100vh - 13.5rem)' }}
          className="table_main_content mt-0 w-full overflow-auto"
        >
          <DynamicTable
            data={workflowListData}
            columns={workflowTableColumns}
            loading={loading}
            enablePagination={false}
            enableSorting
            ServerSidePaginationDetails={{
              totalRecords: totalCount,
              currentPage: page,
              currentSize: size,
              handlePageSizeChange: handlePageSizeChange,
              handlePageNumberChange: handlePageChange,
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default DsrWorkflowTable;
