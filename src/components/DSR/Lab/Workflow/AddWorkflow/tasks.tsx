import React, { useEffect, useState } from 'react';

import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown } from 'lucide-react';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';
import { Button } from '../../../../../@/components/ui/Common/Elements/Button/Button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../../../../../@/components/ui/Common/Elements/Dialog/Dialog';
import { Label } from '../../../../../@/components/ui/Common/Elements/Label/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../../@/components/ui/Common/Elements/Select/Select';
import { Switch } from '../../../../../@/components/ui/switch';
import AutomateIcon from '../../../../../assets/images/Automation Icon.svg';
import eye from '../../../../../assets/info.svg';
import multiPerson from '../../../../../assets/Multiple people icon.svg';
import { setAutomationEnabled } from '../../../../../redux/reducers/DSR/dsr-slice';
import { AppDispatch, RootState } from '../../../../../redux/store';
import {
  AddDsrWorkflowTasksProperties,
  AddWorkFlowDataTableDataItem,
  AutomationInterface,
  AutomationItem,
  AutomationStates,
  NewTaskDataItem,
  TaskFormData,
  TaskStep,
  TaskUser,
} from '../../../../../types/data-subject-rights';
import { getInitialsByName } from '../../../../../utils/helperData';
import AvatarFrame from '../../../../common/Avatar';
import {
  addTask,
  fetchTaskList,
  getAutomationList,
  updateTask,
  updateWorkflowTask,
} from '../../../../common/services/data-subject-request';
import AddWorkflowDataTable from '../../Common/add-workflow-data-table';
import AddTaskModal from '../../Common/Modal/add-task-modal';

const AddDsrWorkflowTasks: React.FC<AddDsrWorkflowTasksProperties> = ({
  isEditMode,
  activeStep,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const [workflowId, setWorkflowId] = useState('');
  const [dsrAddWorkflowTableData, setDsrAddWorkflowTableData] = useState<NewTaskDataItem[]>([]);
  const [automationList, setAutomationList] = useState<AutomationItem[]>([]);
  const [addTaskModalOpen, setAddTaskModalOpen] = useState(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isAutomationEnabled, setIsAutomationEnabled] = useState(false);
  const [selectedAutomation, setSelectedAutomation] = useState('');
  const [currentTaskId, setCurrentTaskId] = useState<number>(-1);
  const stepData = useSelector(
    (state: RootState) => state.dataSubjectRights?.workflow?.RequestTypeStages
  );
  const worflowDetails = useSelector((state: RootState) => state.dataSubjectRights?.workflow);

  const currentStepData = stepData[activeStep];
  const [workflowSteps, setWorkflowSteps] = useState<TaskStep[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [automationStates, setAutomationStates] = useState<AutomationStates>({});
  const [showTaskView, setShowTaskView] = useState<boolean>(false);
  const [isDisableModalOpen, setIsDisableModalOpen] = useState(false);
  const [taskToDisable, setTaskToDisable] = useState<number | null>(null);

  const handleAutomationUpdate = async () => {
    if (!currentTaskId || !selectedAutomation) {
      toast.error('Please select an automation workflow');
      return;
    }
    try {
      toast.loading(t('CommonErrorMessages.UpdatingTaskAutomation'));

      const updateData: AutomationInterface = {
        activepieces_automation_id: selectedAutomation,
      };

      const response = await updateTask(currentTaskId, updateData);

      if (response.success) {
        toast.dismiss();
        toast.success(t('FrontEndErrorMessage.DSR.TaskAutomationUpdatedSuccessfully'));

        setDsrAddWorkflowTableData((prevData) =>
          prevData.map((task) =>
            task.id === currentTaskId
              ? { ...task, activepieces_automation_id: selectedAutomation }
              : task
          )
        );

        setAutomationStates((prev) => ({
          ...prev,
          [currentTaskId]: true,
        }));

        setIsModalOpen(false);
        setSelectedAutomation('');
        dispatch(setAutomationEnabled(true));
      } else {
        toast.dismiss();
        toast.error('Failed to update task automation');
        setAutomationStates((prev) => ({
          ...prev,
          [currentTaskId]: false,
        }));
      }
    } catch (error) {
      toast.dismiss();
      toast.error('Error updating task automation');
      setAutomationStates((prev) => ({
        ...prev,
        [currentTaskId]: false,
      }));
    }
  };

  const handleDisableAutomation = async (taskId: number) => {
    try {
      toast.loading(t('CommonErrorMessages.RemovingAutomation'));

      const updateData = {
        activepieces_automation_id: null,
      };

      const response = await updateTask(taskId, updateData);

      if (response.success) {
        toast.dismiss();
        toast.success(t('FrontEndErrorMessage.DSR.AutomationRemovedSuccessfully'));

        setDsrAddWorkflowTableData((prevData) =>
          prevData.map((task) =>
            task.id === taskId ? { ...task, activepieces_automation_id: null } : task
          )
        );

        setAutomationStates((prev) => ({
          ...prev,
          [taskId]: false,
        }));

        dispatch(setAutomationEnabled(false));
      } else {
        toast.dismiss();
        toast.error('Failed to remove automation');
      }
    } catch (error) {
      toast.dismiss();
      toast.error('Error removing automation');
      console.error('Error removing automation:', error);
    }
  };

  const workflowAddTaskColumns: ColumnDef<AddWorkFlowDataTableDataItem>[] = [
    {
      accessorKey: 'title',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Tasks Title
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const title: string = row.getValue('title');

        return <div className="">{`${title || '-'}`}</div>;
      },
    },
    {
      accessorKey: 'assignee_id',
      header: ({ column }: any) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Assign
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }: any) => {
        const users: TaskUser[] = row.original.users || [];
        const assigneeNames = users
          .map((user: TaskUser) => `${user.firstName} ${user.lastName}`)
          .join(', ');

        if (users.length === 0) return <div>-</div>;

        if (users.length === 1) {
          const user = users[0];
          const fullName = `${user.firstName} ${user.lastName}`;
          return (
            <div className="flex flex-row items-center gap-2 whitespace-nowrap">
              <AvatarFrame value={fullName} getInitials={getInitialsByName} />
              {fullName}
            </div>
          );
        }

        // Multiple users
        const firstUser = users[0];
        const initial = `${firstUser.firstName[0]}` || `${firstUser.lastName[0]}`;

        return (
          <div className="flex-start relative flex items-center">
            <AvatarFrame value={initial} />
            <img
              id={`anchor_elements_${row.original.id}`}
              src={multiPerson}
              alt="Multiple People Logo"
              className="absolute -bottom-px left-8 aspect-square h-10"
            />
            <Tooltip
              anchorSelect={`#anchor_elements_${row.original.id}`}
              style={{
                color: '#FFF',
                fontFamily: 'Poppins',
                fontSize: '14px',
                fontStyle: 'normal',
                fontWeight: '400',
                lineHeight: 'normal',
                borderRadius: '8px',
                backgroundColor: '#000',
                opacity: '0.8',
                display: 'inline-flex',
                padding: '9px 16px',
                justifyContent: 'flex-start',
                alignItems: 'center',
                gap: '10px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
              }}
              content={assigneeNames}
            />
          </div>
        );
      },
    },
    // {
    //   accessorKey: 'created_date',
    //   header: ({ column }) => {
    //     return (
    //       <Button
    //         variant="ghost"
    //         className="p-0"
    //         onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
    //       >
    //         Start Date
    //         <ArrowUpDown className="ml-2 size-4" />
    //       </Button>
    //     );
    //   },
    //   cell: ({ row }) => {
    //     const created_date: string | number | Date = row.getValue('created_date');
    //     return (
    //       <div className="">
    //         {created_date ? convertDateToHumanView(created_date.toString()) : '-'}
    //       </div>
    //     );
    //   },
    // },
    // {
    //   accessorKey: 'end_date',
    //   header: ({ column }) => {
    //     return (
    //       <Button
    //         variant="ghost"
    //         className="p-0"
    //         onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
    //       >
    //         Due Date
    //         <ArrowUpDown className="ml-2 size-4" />
    //       </Button>
    //     );
    //   },
    //   cell: ({ row }) => {
    //     const end_date: string | number | Date = row.getValue('end_date');
    //     return (
    //       <div className="">{end_date ? convertDateToHumanView(end_date.toString()) : '-'}</div>
    //     );
    //   },
    // },

    {
      accessorKey: 'action',
      header: () => {
        return (
          <Button variant="ghost" className="">
            {t('DSR.TaskOverView.Automate')}
          </Button>
        );
      },

      cell: ({ row }) => {
        const rowId = row.original?.id;
        const isAutomated =
          automationStates[rowId] || Boolean(row.original?.activepieces_automation_id);
        const automationId = row.original?.activepieces_automation_id;
        const automationName = automationList.find((auto) => auto.id === automationId)?.label || '';

        return (
          <div
            className="flex items-center gap-1"
            onClick={(e) => {
              e.stopPropagation();
              !isAutomated && fetchWorkflowAutomationList();
            }}
          >
            <Switch
              checked={isAutomated}
              onCheckedChange={(checked) => {
                if (isEditMode) {
                  if (checked) {
                    setCurrentTaskId(rowId);
                    setIsModalOpen(true);
                  } else {
                    setTaskToDisable(rowId);
                    setIsDisableModalOpen(true);
                  }
                }
              }}
              className="data-[state=checked]:bg-[#05CF78]"
              disabled={!isEditMode}
            />
            {isAutomated && (
              <>
                <div className="cursor-pointer">
                  <img
                    title="Automation"
                    id={`anchor_elemen_${row.id}`}
                    src={eye}
                    className="h-6 w-6"
                  />
                </div>
                <Tooltip
                  anchorSelect={`#anchor_elemen_${row.id}`}
                  place="bottom"
                  style={{
                    color: '#FFF',
                    fontFamily: 'Poppins',
                    fontSize: '14px',
                    fontStyle: 'normal',
                    fontWeight: '400',
                    lineHeight: 'normal',
                    borderRadius: '8px',
                    backgroundColor: '#000',
                    opacity: '0.8',
                    display: 'inline-flex',
                    padding: '9px 16px',
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                    gap: '10px',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    zIndex: '999',
                  }}
                  content={automationName}
                />
              </>
            )}
          </div>
        );
      },
    },
  ];

  const extractStepsFromAutomation = (automation: any): TaskStep[] => {
    const steps: TaskStep[] = [];

    // if (automation.version?.trigger?.nextAction?.displayName) {
    //   steps.push({
    //     label: automation.version.trigger?.nextAction?.displayName,
    //     description: ''
    //   });
    // }

    const traverseNextAction = (action: any) => {
      if (action && action.displayName) {
        steps.push({
          label: action.displayName,
          description: '',
        });
        if (action.nextAction) {
          traverseNextAction(action.nextAction);
        }
      }
    };

    if (automation.version?.trigger?.nextAction) {
      traverseNextAction(automation.version.trigger.nextAction);
    }
    return steps;
  };

  // feth workflow automation data
  const fetchWorkflowAutomationList = async () => {
    try {
      setLoading(true);
      const url = '/api/v1/activepieces/workflows';
      const response = await getAutomationList(url);
      if (response?.data?.result?.data) {
        if (response?.data?.result?.data?.length == 0) {
          toast.success(t('FrontEndErrorMessage.DSR.ListIdEmpty'));
          setAutomationList([]);
        } else {
          const listData = response?.data?.result?.data.map((records: any) => {
            const steps = extractStepsFromAutomation(records);
            return {
              value: records?.version?.id.toString(),
              label: records?.version?.displayName,
              id: records?.id,
              steps: steps,
            };
          });
          setAutomationList(listData);
        }
      }
      // setAutomationList(response);
      setLoading(false);
    } catch (err) {
      // setError(`Failed to fetch records`);
      setLoading(false);
      // toast.error(err?.message?err?.message:"Failed to fetch records")
    }
  };
  useEffect(() => {
    const fetchData = async (activeStep: number) => {
      const id = stepData[activeStep].id;
      setWorkflowId(id);
      try {
        setLoading(true);
        // Modify the API call to include the stepType
        const response = await fetchTaskList(id);

        const tasks = response?.data?.result?.map((task: any) => ({
          id: task.id,
          title: task.title || '',
          assignee_id: task.assignee_id || [],
          created_date: task.start_date || '',
          end_date: task.due_date || '',
          department: task.department || '',
          priority: task.priority || '',
          attachment: task.attachment || 0,
          activepieces_automation_id: task.activepieces_automation_id || null,
          guidance_text: task.guidance_text || '',
          users: task.users || [],
          due_days: task.due_days || 0,
        }));

        setDsrAddWorkflowTableData(tasks);
        setLoading(false);
      } catch (err) {
        setError(`Failed to fetch ${activeStep} tasks`);
        setLoading(false);
      }
    };
    fetchData(activeStep);
    setIsAutomationEnabled(false);
    setSelectedAutomation('');
  }, [activeStep]);

  const handleSubmit = async (data: TaskFormData) => {
    toast.loading(t('FrontEndErrorMessage.DSR.AddingTask'));
    try {
      const stage_id = stepData[activeStep].id ? Number(stepData[activeStep].id) : undefined;
      const updatedData = {
        ...data,
        stage_id: stage_id,
        workflow_id: worflowDetails?.id,
        assignee_id: data?.assignee_id,
      };

      const response = await addTask(updatedData);

      if (response.success) {
        const newTask: NewTaskDataItem = {
          id: response.result.id,
          title: response.result.title || '',
          assignee_id: response.result.assignee_id || [],
          created_date: response.result.start_date || new Date(),
          end_date: response.result.due_date || new Date(),
          department: response.result.department || '',
          priority: response.result.priority || '',
          attachment: response.result.attachment || 0,
          activepieces_automation_id: response.result.activepieces_automation_id || null,
          guidance_text: response.result.guidance_text || '',
          users: response.result.users,
          due_days: response.result.due_days,
          documents: [],
          assigned_to: '',
          TaskDocuments: [],
        };

        setDsrAddWorkflowTableData((prevData) => [...prevData, newTask]);

        // Then refresh the entire list to ensure consistency
        try {
          const refreshedTasks = await fetchTaskList(workflowId);
          if (refreshedTasks?.data?.result) {
            const updatedTasks = refreshedTasks.data.result.map((task: any) => ({
              id: task.id,
              title: task.title || '',
              assignee_id: task.assignee_id || [],
              created_date: task.start_date || '',
              end_date: task.due_date || '',
              department: task.department || '',
              priority: task.priority || '',
              attachment: task.attachment || 0,
              activepieces_automation_id: task.activepieces_automation_id || null,
              guidance_text: task.guidance_text || '',
              users: task.users || [],
              due_days: task.due_days || 0,
            }));
            setDsrAddWorkflowTableData(updatedTasks);
          }
        } catch (refreshError) {
          console.error('Error refreshing task list:', refreshError);
          // Even if refresh fails, we still have the immediate update
        }

        toast.dismiss();
        toast.success(t('FrontEndErrorMessage.DSR.TaskAddedSuccessfully'));
        setAddTaskModalOpen(false);
      } else {
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.DSR.FailedToAddTask'));
      }
    } catch (error) {}
  };
  const { t } = useTranslation();

  const handleUpdateTask = async (data: TaskFormData) => {
    toast.loading(t('CommonErrorMessages.UpdatingTask'));
    const stage_id = stepData[activeStep].id ? Number(stepData[activeStep].id) : undefined;

    try {
      const response = await updateWorkflowTask(data.id, {
        title: data.title,
        guidance_text: data.guidance_text,
        stage_id,
        assignee_id: data.assignee_id || [],
        due_days: data.due_days,
      });

      if (response?.success) {
        toast.dismiss();
        toast.success(t('FrontEndErrorMessage.DSR.TaskUpdatedSuccessfully'));

        // Immediately update the table data with the new values
        setDsrAddWorkflowTableData((prevData) => {
          return prevData.map((task) => {
            if (task.id === data.id) {
              return {
                ...task,
                title: data.title,
                guidance_text: data.guidance_text,
                assignee_id: data.assignee_id || [],
                // end_date: data.due_date,
                // created_date: data.start_date,
                users: data.users,
                due_days: data.due_days,
              };
            }
            return task;
          });
        });

        // Optionally refresh the entire task list to ensure consistency
        const refreshedTasks = await fetchTaskList(workflowId);
        if (refreshedTasks?.data?.result) {
          const updatedTasks = refreshedTasks.data.result.map((task: any) => ({
            id: task.id,
            title: task.title || '',
            assignee_id: task.assignee_id || [],
            created_date: task.start_date || '',
            end_date: task.due_date || '',
            department: task.department || '',
            priority: task.priority || '',
            attachment: task.attachment || 0,
            activepieces_automation_id: task.activepieces_automation_id || null,
            guidance_text: task.guidance_text || '',
            users: task.users || [],
            due_days: task.due_days || 0,
          }));
          setDsrAddWorkflowTableData(updatedTasks);
        }
      } else {
        toast.dismiss();
        toast.error(response?.message || 'Failed to update task');
      }
    } catch (error: any) {}
  };

  const handleUpdate = (data: any) => {
    handleUpdateTask(data);
    setShowTaskView(false);
  };

  if (!isEditMode) {
    useEffect(() => {
      fetchWorkflowAutomationList();
    }, []);
  }

  return (
    <div className="flex flex-col gap-4 bg-[#F5F5F5] p-4 md:items-center lg:w-full lg:items-center lg:rounded-lg lg:border">
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogDescription>
              <DialogTitle></DialogTitle>
            </DialogDescription>
            <div className="flex w-full flex-col items-center justify-center space-y-4">
              <div>
                <img src={AutomateIcon} alt="Automate Icon" />
              </div>
              <div>
                <Label className="text-[20px] text-[#5E5E5E]">Automate your process</Label>
              </div>
              <div>
                <Label className="text-[14px] text-[#64748B]">
                  Select an option to enable workflow automation
                </Label>
              </div>
              <div>
                <Select value={selectedAutomation} onValueChange={setSelectedAutomation}>
                  <SelectTrigger className="w-[340px]">
                    <SelectValue placeholder="Select Automation Workflow" />
                  </SelectTrigger>
                  <SelectContent>
                    {automationList.map((option) => (
                      <SelectItem key={option?.id} value={option?.id}>
                        {option?.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </DialogHeader>
          <DialogFooter className="mt-2 flex justify-end">
            <Button variant="outline" onClick={() => setIsModalOpen(false)}>
              {t('Common.Cancel')}
            </Button>
            <Button
              className="bg-custom-primary text-white hover:bg-custom-primary"
              onClick={handleAutomationUpdate}
            >
              {t('DSR.TaskOverView.Automate')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Dialog open={isDisableModalOpen} onOpenChange={setIsDisableModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="text-2xl">Disable Automation</DialogTitle>
            <DialogDescription className="text-xl">
              Are you sure you want to disable automation on this task?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-2 flex justify-end">
            <Button variant="outline" onClick={() => setIsDisableModalOpen(false)}>
              {t('Common.Cancel')}
            </Button>
            <Button
              className="bg-custom-primary text-white hover:bg-custom-primary"
              onClick={() => {
                if (taskToDisable) {
                  handleDisableAutomation(taskToDisable);
                  setIsDisableModalOpen(false);
                  setTaskToDisable(null);
                }
              }}
            >
              Disable Automation
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <div className="flex w-full justify-between">
        <span className="text-xl font-semibold leading-normal text-primary-text">
          {currentStepData?.step_title} Tasks
        </span>
        <div className="flex items-center justify-center gap-3">
          <AddTaskModal
            handleSubmit={(data: TaskFormData) => handleSubmit(data)}
            addTaskModalOpen={addTaskModalOpen}
            setAddTaskModalOpen={() => setAddTaskModalOpen(!addTaskModalOpen)}
            isEditMode={isEditMode}
            isAutomationEnabled={isAutomationEnabled}
          />
        </div>
      </div>
      <div
        className="table_main_content mt-0 w-full overflow-auto rounded-md bg-white"
        style={{ height: 'calc(100vh - 26rem)' }}
      >
        <AddWorkflowDataTable
          data={dsrAddWorkflowTableData}
          columns={workflowAddTaskColumns}
          loading={false}
          handleUpdate={handleUpdate}
          setShowTaskView={setShowTaskView}
          showTaskView={showTaskView}
          userData={[]}
          isEditMode={isEditMode}
        />
      </div>
    </div>
  );
};

export default AddDsrWorkflowTasks;
