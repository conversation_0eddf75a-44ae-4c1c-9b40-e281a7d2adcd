import { Upload } from 'lucide-react';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { Label } from '../../../../../@/components/ui/Common/Elements/Label/Label';
import { Separator } from '../../../../../@/components/ui/Common/Elements/Seperator/Seperator';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '../../../../../@/components/ui/Common/Elements/Tabs/Tabs';
import { Textarea } from '../../../../../@/components/ui/textarea';
import logo from '../../../../../assets/gotrustTitle_light.svg';
import { LeftNewFormBuilderProps } from '../../../../../types/data-subject-rights';
import { uploadDocument } from '../../../../common/services/data-subject-request';
import { DSRSidebar } from './sidebar';

export default function LeftNewFormBuilder(
  props: LeftNewFormBuilderProps & {
    logoHeight: number;
    onLogoHeightChange: (height: number) => void;
    logoWidth: number;
    onLogoWidthChange: (width: number) => void;
  }
) {
  const {
    onLogoChange,
    onHeaderDescriptionChange,
    initialLogoUrl,
    logoHeight,
    onLogoHeightChange,
    logoWidth,
    onLogoWidthChange,
    activeTab,
    setActiveTab,
    initialHeaderDescription,
  } = props;
  const { t } = useTranslation();
  const [logoUrl, setLogoUrl] = useState<string>(initialLogoUrl || logo);
  const [headerDescription, setHeaderDescription] = useState<string>(
    initialHeaderDescription ||
      'GoTrust Inc., including all its affiliates ("Company", "we," "our," and "us"), values the privacy rights of our customers, business partners, suppliers, vendors, users, and others. As required under applicable law, and specifically under the EU General Data Protection Regulation ("GDPR"), UK GDPR, and the California Consumer Privacy Act of 2018 ("CCPA") (collectively "Data Protection Laws"), individuals (including European Union and UK residents, and California residents, respectively) are permitted to make certain requests regarding our processing of their Personal Data.'
  );

  useEffect(() => {
    setLogoUrl(initialLogoUrl || logo);
  }, [initialLogoUrl]);

  useEffect(() => {
    if (initialHeaderDescription) {
      setHeaderDescription(initialHeaderDescription);
    }
  }, [initialHeaderDescription]);

  const handleHeaderDescriptionChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newDescription = event.target.value;
    setHeaderDescription(newDescription);
    onHeaderDescriptionChange(newDescription);
  };

  const handleLogoHeightChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onLogoHeightChange(Number(event.target.value));
  };
  const handleLogoWidthChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onLogoWidthChange(Number(event.target.value));
  };

  const handleLogoChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      toast.loading(t('DSR.FormBuilder.UploadingLogo'));
      try {
        const response = await uploadDocument(file);
        const newLogoUrl = response.result[0].url;
        setLogoUrl(newLogoUrl);
        onLogoChange(newLogoUrl);
        toast.dismiss();
        toast.success(t('DSR.FormBuilder.LogoUploadedSuccessfully'));
      } catch (error) {
        toast.dismiss();
        toast.error(t('DSR.FormBuilder.FailedToUploadLogo'));
      }
    }
  };

  return (
    <div className="w-full overflow-auto rounded-md bg-white py-2 lg:w-80">
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger
            value="basic"
            onClick={() => setActiveTab('basic')}
            className="data-[state=active]:bg-primary data-[state=active]:text-white"
          >
            Basic
          </TabsTrigger>
          <TabsTrigger
            onClick={() => setActiveTab('elements')}
            className="data-[state=active]:bg-primary data-[state=active]:text-white"
            value="elements"
          >
            Elements
          </TabsTrigger>
        </TabsList>
        <TabsContent value="basic" className="space-y-6">
          <Separator className="bg-gray-300" />
          <div className="px-3">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Upload Logo</Label>
              <div className="flex flex-col gap-2 rounded-lg border border-gray-400 p-2">
                {logoUrl && (
                  <div className="mt-2">
                    <img
                      src={logoUrl}
                      alt="Logo"
                      className="mx-auto h-20 w-full rounded-md object-contain"
                    />
                  </div>
                )}
                <label
                  htmlFor="logo-upload"
                  className="hover:bg-primary-dark mt-2 inline-flex cursor-pointer items-center justify-center gap-2 rounded-md bg-primary px-4 py-2 text-white"
                >
                  <Upload className="h-5 w-5 text-center" /> Upload Logo
                </label>
                <input
                  type="file"
                  accept=".jpg, .jpeg, .png"
                  onChange={handleLogoChange}
                  id="logo-upload"
                  style={{ display: 'none' }}
                />
              </div>

              {/* Improved Logo Size Controls */}
              <div className="mt-4 space-y-4 rounded-lg border border-gray-200 p-3">
                <h3 className="text-sm font-medium text-gray-700">Logo Size</h3>

                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs font-medium text-gray-600">Height</Label>
                    <span className="text-xs font-medium">{logoHeight}px</span>
                  </div>
                  <input
                    type="range"
                    min={40}
                    max={160}
                    value={logoHeight}
                    onChange={handleLogoHeightChange}
                    className="w-full cursor-pointer accent-custom-primary"
                  />
                </div>

                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs font-medium text-gray-600">Width</Label>
                    <span className="text-xs font-medium">{logoWidth}px</span>
                  </div>
                  <input
                    type="range"
                    min={40}
                    max={350}
                    value={logoWidth}
                    onChange={handleLogoWidthChange}
                    className="w-full cursor-pointer accent-custom-primary"
                  />
                </div>
              </div>
            </div>
            <div className="mt-4 space-y-2">
              <Label className="text-sm font-medium">Header Description</Label>
              <Textarea
                placeholder="Enter description"
                className="h-52 bg-gray-50 text-sm scrollbar-hide"
                value={headerDescription}
                onChange={handleHeaderDescriptionChange}
              />
            </div>
          </div>
        </TabsContent>
        <TabsContent value="elements" className="space-y-6">
          <Separator className="bg-gray-300" />
          <div className="px-3">
            <DSRSidebar />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
