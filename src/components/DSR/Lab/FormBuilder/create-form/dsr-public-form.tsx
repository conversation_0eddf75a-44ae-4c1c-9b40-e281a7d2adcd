import { format } from 'date-fns';
import { E164Number } from 'libphonenumber-js';
import { CalendarIcon, Globe } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { DateRange } from 'react-day-picker';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from '../../../../../@/components/ui/Common/Elements/Button/ButtonSort';
import { Calendar } from '../../../../../@/components/ui/Common/Elements/Calendar/Calendar';
import { Label } from '../../../../../@/components/ui/Common/Elements/Label/Label';
import { PhoneInput } from '../../../../../@/components/ui/Common/Elements/PhoneInput/phone-input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../../../../../@/components/ui/Common/Elements/Popover/Popover';
import { Separator } from '../../../../../@/components/ui/Common/Elements/Seperator/Seperator';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../../../../../@/components/ui/dropdown-menu';
import { Input } from '../../../../../@/components/ui/Input';
import attachment from '../../../../../assets/addAttachment.svg';
import gotrustTitle from '../../../../../assets/gotrustTitle_light.svg';
import {
  DsrPublicContentData,
  DsrPublicFormElement,
  DsrPublicFormValues,
} from '../../../../../types/data-subject-rights';
import { decryptId } from '../../../../../utils/cipher';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../../@/components/ui/Common/Elements/Select/Select';
import {
  fetchCustomEndpointData,
  getDsrFormData,
  getTranslatedContent,
  getTranslatedLanguage,
  submitFormBuilder,
  uploadMultipleGuestDocuments,
  uploadMultiplePublicDocuments,
} from '../../../../common/services/data-subject-request';

interface Language {
  language_code: string;
  language: string;
  name?: string;
}

function RequestForm() {
  const { encryptedFormId, encryptedCustomerId } = useParams<{
    encryptedFormId: string;
    encryptedCustomerId: string;
  }>();

  const [date, setDate] = useState<DateRange | undefined>({
    from: undefined,
    to: undefined,
  });
  const [formData, setFormData] = useState<DsrPublicFormElement[]>([]);
  const [formValues, setFormValues] = useState<DsrPublicFormValues>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<Record<string, File[]>>({});
  const navigate = useNavigate();
  const [submitting, setSubmitting] = useState(false);
  const [contentData, setContentData] = useState<DsrPublicContentData | null>(null);
  const [layoutPreference, setLayoutPreference] = useState<number>(1);
  const [visibleElements, setVisibleElements] = useState<Set<string>>(new Set());
  const [languages, setLanguages] = useState<Language[]>([]);
  const [selectedLanguage, setSelectedLanguage] = useState(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const languageFromQuery = searchParams.get('language_code');
    if (languageFromQuery) {
      return languageFromQuery;
    }
    return navigator.language.split('-')[0] ?? 'en';
  });
  const [translatedTexts, setTranslatedTexts] = useState<Record<string, string>>({});

  const [languageChanging, setLanguageChanging] = useState(false);

  const [loadingTranslatedLanguages, setLoadingTranslatedLanguages] = useState(false);

  const { t } = useTranslation();

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
    elementId: string
  ) => {
    const selectedFiles = Array.from(event.target.files || []);

    // Validate maximum 5 files
    if (selectedFiles.length > 5) {
      toast.error('Maximum 5 files can be uploaded at once');
      // Clear the input
      event.target.value = '';
      return;
    }

    if (selectedFiles.length > 0) {
      // Update the file state for this specific element
      setUploadedFiles((prev) => ({ ...prev, [elementId]: selectedFiles }));

      const fileElement = formData.find((element) => element.id === elementId);
      if (fileElement) {
        try {
          let uploadResponse;

          // Use the appropriate upload function based on endpoint and file count
          if (fileElement?.endpoint) {
            // Use the specific endpoint if available
            uploadResponse = await uploadMultiplePublicDocuments(
              fileElement.endpoint,
              selectedFiles
            );
          } else {
            // Use the default UPLOAD_GUEST_REQUESTS endpoint when no specific endpoint is provided
            uploadResponse = await uploadMultipleGuestDocuments(selectedFiles);
          }

          if (uploadResponse?.success) {
            // Map the uploaded files to the format expected by the form
            const uploadResults = uploadResponse.result.map((result: any, index: number) => ({
              url: result.url,
              original_name: selectedFiles[index].name,
            }));

            // Store all uploaded files data in formValues
            handleInputChange(fileElement?.id, uploadResults);
            toast.success(t('ToastMessages.Files.FileUploadedSuccessfully'));
          } else {
            throw new Error('Failed to upload files');
          }
        } catch (error) {
          console.error('Error uploading files:', error);
          toast.error(t('FrontEndErrorMessage.FileUpload.FailedToUploadFile'));
          setUploadedFiles((prev) => {
            const newFiles = { ...prev };
            delete newFiles[elementId];
            return newFiles;
          });
        }
      }
    }
  };

  const translateFormContent = async (language: string) => {
    if (language === 'en') {
      setTranslatedTexts({});
      setLanguageChanging(false);
      return;
    }

    setLanguageChanging(true);
    try {
      const allTranslatedTexts: Record<string, string> = {};

      // Initialize with empty translations object
      // All translations (static + dynamic) will come from backend API

      // Then, get translated content from the API for the selected language
      if (encryptedFormId && encryptedCustomerId) {
        try {
          const translatedContentResponse = await getTranslatedContent(
            encryptedFormId,
            encryptedCustomerId,
            language
          );

          if (translatedContentResponse?.success && translatedContentResponse?.result) {
            const translatedData = translatedContentResponse.result;

            // The API returns a single object with the selected language content
            if (
              translatedData &&
              translatedData.content &&
              translatedData.language_code === language
            ) {
              // Handle form content translations (title, paragraph, etc.)
              if (translatedData.content.content) {
                Object.keys(translatedData.content.content).forEach((key) => {
                  allTranslatedTexts[key] = translatedData.content.content[key];
                });
              }

              // Handle paragraphContent translation (dynamic content from form builder)
              if (translatedData.content.paragraphContent) {
                allTranslatedTexts['paragraphContent'] = translatedData.content.paragraphContent;
              }

              // Handle static form text translations
              const staticTextMappings = {
                formTitle: 'Data Subject Request Form',
                submitButton: 'Submit Request',
                submittingText: 'Submitting...',
                poweredBy: 'Powered by',
                selectOptionDefault: 'Select an option',
                pickDateDefault: 'Pick a date',
                uploadFileText: 'Upload Files',
                changeFileText: 'Change Files',
                selectedFileText: 'Selected files:',
                emailValidationError: 'Please enter a valid email address',
                enterPlaceholder: 'Enter',
                fileSizeLimitText: '(Max file: 5 and Max size: 100MB)',
              };

              Object.keys(staticTextMappings).forEach((key) => {
                if (translatedData.content[key]) {
                  allTranslatedTexts[key] = translatedData.content[key];
                }
              });

              // Handle DSRCustomerControls translations
              if (translatedData.content.DSRCustomerControls) {
                translatedData.content.DSRCustomerControls.forEach((control: any) => {
                  // Map control title
                  if (control.title) {
                    allTranslatedTexts[`label_${control.id}`] = control.title;
                  }

                  // Map control placeholder
                  if (control.placeholder) {
                    allTranslatedTexts[`placeholder_${control.id}`] = control.placeholder;
                  }

                  // Map control field options
                  if (control.fields) {
                    control.fields.forEach((field: any, index: number) => {
                      if (field.name) {
                        allTranslatedTexts[`option_${control.id}_${index}`] = field.name;
                      }
                    });
                  }
                });
              }

              // Handle direct content mapping (for backward compatibility)
              Object.keys(translatedData.content).forEach((questionId) => {
                if (questionId !== 'content' && questionId !== 'DSRCustomerControls') {
                  const translatedText = translatedData.content[questionId];
                  if (typeof translatedText === 'string') {
                    allTranslatedTexts[`label_${questionId}`] = translatedText;
                  }
                }
              });
            }
          } else {
            // If translation not found, log and continue without translation
            console.log(`Translation not found for language: ${language}`);
          }
        } catch (apiError: any) {
          console.log(
            `No translated content found for language ${language}:`,
            apiError?.response?.data?.message || apiError.message
          );
          // Don't show error toast for missing translations, just continue without translation
        }
      }

      // Set all translated texts (static + dynamic content)
      setTranslatedTexts(allTranslatedTexts);
    } catch (error) {
      console.error('Translation error:', error);
      // Only show error toast for actual errors, not missing translations
      if (error instanceof Error && !error.message.includes('404')) {
        toast.error(t('FrontEndErrorMessage.Translation.FailedToTranslateContent'));
      }
    } finally {
      setLanguageChanging(false);
    }
  };
  // Only fetch form content when encryptedFormId or encryptedCustomerId changes (not on language change)
  useEffect(() => {
    const fetchFormContent = async () => {
      try {
        if (!encryptedFormId || !encryptedCustomerId) {
          throw new Error('Invalid URL parameters');
        }

        setLoading(true);

        // Use getDsrFormData for original form content
        const formDataResponse = await getDsrFormData(encryptedCustomerId, encryptedFormId);

        if (formDataResponse?.success && formDataResponse?.result) {
          const formData = formDataResponse.result;
          console.log('API Response:', formData); // Debug log

          // Set content data from the response
          if (formData.content) {
            setContentData(formData.content);
            setLayoutPreference(formData.content.layoutPreference || 1);
          }

          // Set form data from DSRCustomerControls
          if (formData.DSRCustomerControls) {
            const transformedData: DsrPublicFormElement[] = await Promise.all(
              formData.DSRCustomerControls.map(async (item: any) => {
                let options =
                  item.fields?.map((field: any) => ({
                    id: field.id.toString(),
                    label: field.name.replace(/_/g, ' '),
                    value: field?.name,
                  })) || [];

                // Handle any field with endpoint data (not just custom_select), but skip for file fields
                if (item.endpoint && item.artifact_type !== 'file') {
                  try {
                    // Decrypt the customer ID for the endpoint call
                    const decryptedCustomerId = decryptId(encryptedCustomerId);
                    // Pass busi_unit_id as the third argument (language code not supported by endpoint)
                    const endpointResponse = await fetchCustomEndpointData(
                      item.endpoint,
                      decryptedCustomerId,
                      formData?.busi_unit_id
                    );

                    if (endpointResponse.success && endpointResponse.result) {
                      const endpointData = Array.isArray(endpointResponse.result)
                        ? endpointResponse.result
                        : [];

                      if (item.endpoint === '/guest-workflow/published') {
                        options = endpointData.map((data: any) => ({
                          id: data?.id?.toString(),
                          label: data?.flowtype?.replace(/_/g, ' '),
                          value: data?.id,
                        }));
                      } else if (item.endpoint === '/country') {
                        options = endpointData.map((data: any) => {
                          const originalCountryName = data?.country_name?.replace(/_/g, ' ');
                          let translatedCountryName = originalCountryName;

                          // Use browser's built-in internationalization for country names
                          if (selectedLanguage !== 'en' && data?.country_code) {
                            try {
                              const displayNames = new Intl.DisplayNames([selectedLanguage], {
                                type: 'region',
                              });
                              translatedCountryName =
                                displayNames.of(data.country_code.toUpperCase()) ||
                                originalCountryName;
                            } catch (error) {
                              // Fallback to original name if translation fails
                              translatedCountryName = originalCountryName;
                            }
                          }

                          return {
                            id: data?.id?.toString(),
                            label: translatedCountryName,
                            value: data?.id,
                          };
                        });
                      } else {
                        options = endpointData.map((data: any) => ({
                          id: data?.id?.toString(),
                          label: data?.name || data?.label || data?.value,
                          value: data?.value || data?.id,
                        }));
                      }
                    }
                  } catch (error) {
                    console.error(`Error fetching endpoint data for ${item.endpoint}:`, error);
                    // Keep original options if endpoint fails
                  }
                }

                return {
                  id: item.id.toString(),
                  type:
                    item.artifact_type === 'input'
                      ? 'text'
                      : item.artifact_type === 'custom_select'
                        ? 'select'
                        : item.artifact_type,
                  label: item.title,
                  placeholder: item.placeholder || translatedTexts['enterPlaceholder'] || 'Enter',
                  required: !item.is_optional,
                  question: '',
                  helpText: '',
                  options,
                  is_optional: item.is_optional,
                  endpoint: item.endpoint,
                  rules: item.rules || [],
                  rule_applied: item.rule_applied === '1',
                };
              })
            );

            setFormData(transformedData);
            console.log('Transformed Form Data:', transformedData); // Debug log

            // Initialize form values
            const initialValues: DsrPublicFormValues = {};
            transformedData.forEach((element: DsrPublicFormElement) => {
              if (element.type === 'checkbox') {
                initialValues[element.id] = [];
              } else if (element.type === 'file') {
                initialValues[element.id] = null;
              } else {
                initialValues[element.id] = '';
              }
            });
            setFormValues(initialValues);
          }
        } else {
          console.error('API Error:', formDataResponse);
          throw new Error(formDataResponse?.message || 'Failed to fetch form content');
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch form content';
        console.error('Form loading error:', errorMessage);
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchFormContent();
  }, [encryptedFormId, encryptedCustomerId]);

  // Only translate form content on language change
  useEffect(() => {
    if (selectedLanguage && formData.length > 0 && contentData) {
      translateFormContent(selectedLanguage);
    }
  }, [selectedLanguage, formData.length, contentData !== null]);

  const fetchTranslatedLanguages = async () => {
    if (!encryptedCustomerId || !encryptedFormId) {
      return;
    }

    setLoadingTranslatedLanguages(true);
    try {
      const response = await getTranslatedLanguage(encryptedFormId, encryptedCustomerId);
      if (
        response &&
        response.result &&
        response.result.rows &&
        Array.isArray(response.result.rows)
      ) {
        setLanguages(response.result.rows);
      }
    } catch (error) {
      console.error('Failed to fetch translated languages:', error);
    } finally {
      setLoadingTranslatedLanguages(false);
    }
  };

  useEffect(() => {
    fetchTranslatedLanguages();
  }, [encryptedCustomerId, encryptedFormId]);

  useEffect(() => {
    if (formData.length > 0) {
      const initialVisible = new Set<string>();
      formData.forEach((element) => {
        initialVisible.add(element.id);
      });
      setVisibleElements(initialVisible);
    }
  }, [formData]);

  const applyRules = (changedElementId: string, newValue: string) => {
    const changedElement = formData.find((el) => el.id === changedElementId);

    if (!changedElement || !changedElement.rules || changedElement.rules.length === 0) {
      return;
    }

    setVisibleElements((prevVisible) => {
      const newVisible = new Set(prevVisible);
      let hasChanges = false;

      changedElement.rules?.forEach((rule) => {
        // Handle both string and number comparisons for rule options
        // Convert both values to strings for consistent comparison
        const ruleOptionValue = String(rule.options);
        const currentValue = String(newValue);

        if (ruleOptionValue === currentValue) {
          rule.questionList?.forEach((questionId) => {
            if (rule.showOrHide === 'hide') {
              if (newVisible.has(questionId)) {
                newVisible.delete(questionId);
                hasChanges = true;
              }
            } else if (rule.showOrHide === 'show') {
              if (!newVisible.has(questionId)) {
                newVisible.add(questionId);
                hasChanges = true;
              }
            }
          });
        } else {
          rule.questionList?.forEach((questionId) => {
            if (rule.showOrHide === 'hide') {
              if (!newVisible.has(questionId)) {
                newVisible.add(questionId);
                hasChanges = true;
              }
            } else if (rule.showOrHide === 'show') {
              if (newVisible.has(questionId)) {
                newVisible.delete(questionId);
                hasChanges = true;
              }
            }
          });
        }
      });

      return hasChanges ? newVisible : prevVisible;
    });
  };

  const handleInputChange = (
    elementId: string,
    value:
      | string
      | string[]
      | File
      | Date
      | null
      | { url: string; original_name: string }
      | { url: string; original_name: string }[]
  ) => {
    setFormValues((prev) => ({
      ...prev,
      [elementId]: value,
    }));
  };

  const getMaxDate = () => {
    const today = new Date();
    return new Date(today.getFullYear() - 18, today.getMonth(), today.getDate());
  };
  const maxDate = getMaxDate();

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const renderFormElement = (element: DsrPublicFormElement) => {
    const { id, type, label, required, helpText, options } = element;
    const isEmailField = label.toLowerCase().includes('email');

    const translatedPlaceholder =
      translatedTexts[`placeholder_${element.id}`] ||
      element.placeholder ||
      translatedTexts['enterPlaceholder'] ||
      'Enter';
    const translatedHelpText = translatedTexts[`helpText_${element.id}`] || element.helpText;

    switch (type) {
      case 'text':
      case 'email':
      case 'input':
        if (label.toLowerCase().includes('phone number')) {
          return (
            <div>
              <PhoneInput
                value={formValues[id] as E164Number}
                onChange={(value) => {
                  handleInputChange(id, value);
                  if (element.rule_applied) {
                    applyRules(id, value || '');
                  }
                }}
                className="w-full p-1"
                placeholder={translatedPlaceholder}
              />
            </div>
          );
        }
        return (
          <div>
            <Input
              type={type}
              placeholder={translatedPlaceholder}
              required={required}
              value={formValues[id] as string}
              onChange={(e) => {
                const value = e.target.value;
                handleInputChange(id, value);
                if (element.rule_applied) {
                  applyRules(id, value);
                }
              }}
              className="mt-1 w-full"
            />
            {isEmailField && formValues[id] && !validateEmail(formValues[id] as string) && (
              <p className="mt-1 text-sm text-red-500">
                {translatedTexts['emailValidationError'] || 'Please enter a valid email address'}
              </p>
            )}
            {helpText && <p className="mt-1 text-sm text-gray-500">{translatedHelpText}</p>}
          </div>
        );

      case 'textarea':
        return (
          <>
            <textarea
              className="mt-1 block w-full rounded-md border border-gray-300 p-2"
              placeholder={translatedPlaceholder}
              required={required}
              value={formValues[id] as string}
              onChange={(e) => {
                const value = e.target.value;
                handleInputChange(id, value);
                if (element.rule_applied) {
                  applyRules(id, value);
                }
              }}
            />
          </>
        );

      case 'select':
        const selectValue = formValues[id];
        return (
          <>
            <Select
              required={required}
              value={selectValue != null ? String(selectValue) : ''}
              onValueChange={(value) => {
                handleInputChange(id, value);
                if (element.rule_applied) {
                  applyRules(id, value);
                }
              }}
            >
              <SelectTrigger className="mt-1 rounded-md border border-gray-300 p-2">
                <SelectValue placeholder={translatedPlaceholder} />
              </SelectTrigger>
              <SelectContent>
                {options?.map((option, optionIndex) => (
                  <SelectItem key={option.id} value={String(option.value)}>
                    {element.endpoint
                      ? option?.label
                      : translatedTexts[`option_${id}_${optionIndex}`] ||
                        option?.label?.replace(/_/g, ' ')}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </>
        );
      case 'radio':
        return (
          <>
            <div className="mt-1 space-y-2">
              <div className="flex flex-row gap-5">
                {options?.map((option, optionIndex) => (
                  <div key={option.id} className="flex">
                    <input
                      type="radio"
                      id={`${id}-${option.id}`}
                      name={id}
                      value={option.value}
                      checked={(formValues[id] as string) === option.value}
                      onChange={(e) => {
                        handleInputChange(id, e.target.value);
                        if (element.rule_applied) {
                          applyRules(id, e.target.value);
                        }
                      }}
                      required={required}
                      className="h-4 w-4 cursor-pointer border-gray-300"
                    />
                    <label
                      htmlFor={`${id}-${option.id}`}
                      className="ml-2 block text-sm text-gray-700"
                    >
                      {element.endpoint
                        ? option.label
                        : translatedTexts[`option_${id}_${optionIndex}`] ||
                          option.label.replace(/_/g, ' ')}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </>
        );
      case 'checkbox':
        return (
          <div className="mt-1 space-y-2">
            {options?.map((option, optionIndex) => (
              <div key={option.id} className="flex items-center">
                <input
                  type="checkbox"
                  id={`${id}-${option.id}`}
                  value={option.value}
                  checked={(formValues[id] as string[]).includes(option.value)}
                  onChange={(e) => {
                    const currentValues = formValues[id] as string[];
                    const newValues = e.target.checked
                      ? [...currentValues, option.value]
                      : currentValues.filter((v) => v !== option.value);
                    handleInputChange(id, newValues);
                    if (element.rule_applied) {
                      // For checkboxes, apply rules based on the selected option value
                      applyRules(id, option.value);
                    }
                  }}
                  className="h-4 w-4 rounded border-gray-300"
                />
                <label htmlFor={`${id}-${option.id}`} className="ml-2 block text-sm text-gray-700">
                  {element.endpoint
                    ? option.label
                    : translatedTexts[`option_${id}_${optionIndex}`] || option.label}
                </label>
              </div>
            ))}
          </div>
        );

      case 'file':
        const currentFiles = uploadedFiles[id] || [];
        return (
          <div>
            <input
              type="file"
              id={`fileInput-${id}`}
              accept=".jpg,.jpeg,.png,.pdf,.doc,.docx, .xlsx"
              onChange={(event) => handleFileChange(event, id)}
              style={{ display: 'none' }}
              title="Upload Files"
              multiple
            />
            <Button
              type="button"
              variant="outline"
              className="flex min-w-28 items-center gap-1"
              onClick={() => document.getElementById(`fileInput-${id}`)?.click()}
            >
              <img src={attachment} alt="attachment" className="size-4" />
              {currentFiles.length > 0
                ? translatedTexts['changeFileText'] || 'Change Files'
                : translatedTexts['uploadFileText'] || 'Upload Files'}
            </Button>
            <p className="mt-2 text-xs">
              {translatedTexts?.fileSizeLimitText || '(Max file: 5 and Max size: 100MB)'}
            </p>
            {currentFiles.length > 0 && (
              <div className="mt-2">
                <p className="text-sm text-gray-600">
                  {translatedTexts['selectedFileText'] || 'Selected files:'} ({currentFiles.length})
                </p>
                <ul className="mt-1 max-h-24 overflow-y-auto text-sm text-gray-600">
                  {currentFiles.map((file, index) => (
                    <li key={index} className="truncate">
                      {file.name}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        );

      case 'date':
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="flex h-12 w-full justify-between">
                {formValues[id] instanceof Date ? (
                  format(formValues[id] as Date, 'PPP')
                ) : (
                  <span>{translatedTexts['pickDateDefault'] || 'Pick a date'}</span>
                )}
                <CalendarIcon className="ml-auto size-4 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0" align="start">
              <Calendar
                mode="single"
                captionLayout="dropdown-buttons"
                selected={formValues[id] as Date}
                onSelect={(selectedDate) => {
                  handleInputChange(id, selectedDate || null);
                  if (element.rule_applied && selectedDate) {
                    // For date fields, apply rules based on the formatted date string
                    applyRules(id, format(selectedDate, 'PPP'));
                  }
                }}
                disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                initialFocus
                fromYear={1920}
                toYear={maxDate.getFullYear()}
                className="w-full"
              />
            </PopoverContent>
          </Popover>
        );
      case 'number':
        return (
          <>
            <Input
              type="number"
              className="mt-1 block w-full rounded-md border border-gray-300 p-2"
              placeholder={translatedPlaceholder}
              required={required}
              value={formValues[id] as string}
              onChange={(e) => {
                const value = e.target.value;
                handleInputChange(id, value);
                if (element.rule_applied) {
                  applyRules(id, value);
                }
              }}
            />
          </>
        );

      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></div>
      </div>
    );
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);
    const toastId = toast.loading(t('FrontEndErrorMessage.DSR.SubmittingForm'));

    try {
      const answers: { [key: string]: string | { url: string; original_name: string }[] } = {};

      // Handle all file elements
      const fileElements = formData.filter((element) => element.type === 'file');
      const fileElementIds = new Set(fileElements.map((el) => el.id));

      Object.keys(formValues).forEach((key) => {
        if (fileElementIds.has(key) && formValues[key]) {
          // Handle file elements - check if it's already an array or single object
          const fileValue = formValues[key];
          if (Array.isArray(fileValue)) {
            answers[key] = fileValue as { url: string; original_name: string }[];
          } else {
            answers[key] = [
              fileValue as {
                url: string;
                original_name: string;
              },
            ];
          }
        } else if (!fileElementIds.has(key)) {
          // Handle non-file elements
          if (Array.isArray(formValues[key])) {
            answers[key] = (formValues[key] as string[]).join(',');
          } else {
            answers[key] = String(formValues[key] || '');
          }
        }
      });

      const payload = {
        answers,
        form_id: encryptedFormId,
        customer_id: encryptedCustomerId,
      };

      const response = await submitFormBuilder(payload);

      if (response && response.success) {
        if (response.result?.request_id) {
          localStorage.setItem('dsrRequestId', response.result.request_id.toString());
        }

        toast.success(t('FrontEndErrorMessage.DSR.FormSubmittedSuccessfully'), {
          id: toastId,
        });

        navigate('/dsr/verify-otp', {
          state: {
            formValues: answers,
            requestId: response.result?.request_id,
            encryptedFormId: encryptedFormId,
            encryptedCustomerId: encryptedCustomerId,
            authenticationType: response.result?.authentication_type,
          },
        });
      } else {
        resetForm();
        toast.error(response?.message || t('FrontEndErrorMessage.DSR.FailedToSubmitForm'), {
          id: toastId,
        });
        throw new Error(response?.message || t('FrontEndErrorMessage.DSR.FailedToSubmitForm'));
      }
    } catch (error: any) {
      toast.error(t('FrontEndErrorMessage.DSR.FailedToSubmitForm'));
      console.error('Error submitting form:', error);
      resetForm();
    } finally {
      setSubmitting(false);
    }
  };

  const resetForm = () => {
    const initialValues: DsrPublicFormValues = {};
    formData.forEach((element: DsrPublicFormElement) => {
      if (element.type === 'checkbox') {
        initialValues[element.id] = [];
      } else if (element.type === 'file') {
        initialValues[element.id] = null;
      } else {
        initialValues[element.id] = '';
      }
    });

    setFormValues(initialValues);
    setUploadedFiles({});
    setDate({
      from: undefined,
      to: undefined,
    });

    // Reset all file inputs
    const fileInputs = document.querySelectorAll(
      'input[type="file"]'
    ) as NodeListOf<HTMLInputElement>;
    fileInputs.forEach((input) => {
      input.value = '';
    });
  };
  return (
    <div className="h-screen overflow-y-auto bg-gray-50 p-3">
      <form
        onSubmit={handleSubmit}
        className="max-w-full overflow-hidden rounded-lg bg-white p-8 shadow-lg lg:mx-auto lg:max-w-[70vw]"
      >
        <div className="flex items-center justify-end pb-1">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="flex items-center outline-none">
                {languageChanging ? (
                  <div className="flex items-center">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-400 border-t-transparent"></div>
                  </div>
                ) : (
                  <>
                    <Globe className="cursor-pointer" size={20} />
                    <span className="ml-1 text-base">{selectedLanguage.toUpperCase()}</span>
                  </>
                )}
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="max-h-[450px] min-h-[130px] w-56 overflow-auto">
              <DropdownMenuLabel>Select Language</DropdownMenuLabel>
              <DropdownMenuSeparator>
                {loadingTranslatedLanguages ? (
                  <div className="flex justify-center p-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-400 border-t-transparent"></div>
                  </div>
                ) : (
                  <DropdownMenuGroup defaultValue={selectedLanguage}>
                    {/* Always show English as first option */}
                    <DropdownMenuItem
                      className="cursor-pointer"
                      textValue="English"
                      onClick={() => setSelectedLanguage('en')}
                      key="en"
                    >
                      English
                    </DropdownMenuItem>
                    {/* Show other translated languages */}
                    {languages.length > 0 &&
                      languages.map((lang) => (
                        <DropdownMenuItem
                          className="cursor-pointer"
                          textValue={lang.language}
                          onClick={() => setSelectedLanguage(lang.language_code)}
                          key={lang.language_code}
                        >
                          {lang.name ? lang.name : lang.language}
                        </DropdownMenuItem>
                      ))}
                  </DropdownMenuGroup>
                )}
              </DropdownMenuSeparator>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        {contentData && (
          <>
            <div
              style={{
                backgroundColor: contentData?.headerBackgroundColor,
                color: contentData?.headerTextColor,
                padding: '1rem',
                borderTopRightRadius: '0.5rem',
                borderTopLeftRadius: '0.5rem',
                marginBottom: '1.5rem',
              }}
            >
              <div className="flex flex-col items-center justify-between gap-4 sm:flex-row">
                <h1 className="text-2xl font-bold sm:flex-1">
                  {translatedTexts['formTitle'] || 'Data Subject Request Form'}
                </h1>

                {contentData.logoUrl && (
                  <img
                    src={contentData.logoUrl}
                    alt="Logo"
                    className="h-12 w-auto max-w-[200px] object-contain sm:h-16 sm:max-w-[250px]"
                    style={{
                      height: `${contentData.logoHeight}px`,
                      width: `${contentData.logoWidth}px`,
                      maxWidth: '200px',
                    }}
                  />
                )}
                {/* <img src={logoUrl} alt="Logo" style={{ height: `${logoHeight}px`, width: `${logoWidth}px`, maxWidth: '200px' }} className="rounded-md" /> */}
              </div>
            </div>
            {contentData.paragraphContent && (
              <p className="mb-2 mt-2 text-sm">
                {translatedTexts['paragraphContent'] || contentData.paragraphContent}
              </p>
            )}
          </>
        )}
        <Separator className="my-4 bg-gray-200" />

        <div className={`grid ${layoutPreference === 2 ? 'grid-cols-2' : 'grid-cols-1'} gap-6`}>
          {formData.map(
            (element) =>
              visibleElements.has(element.id) && (
                <div key={element.id} className="overflow-auto p-1">
                  <label className="block text-sm font-medium text-gray-700">
                    {translatedTexts[`label_${element.id}`] || element.label}
                    {!element.is_optional && <span className="ml-1 text-red-500">*</span>}
                  </label>

                  {element.helpText && (
                    <p className="mt-1 text-sm text-gray-500">
                      {translatedTexts[`helpText_${element.id}`] || element.helpText}
                    </p>
                  )}
                  {renderFormElement(element)}
                </div>
              )
          )}
        </div>

        <div className="flex justify-end pt-6">
          <button
            type="submit"
            disabled={submitting}
            className="flex items-center justify-center rounded-md bg-custom-primary px-4 py-2 text-white hover:bg-custom-primary focus:outline-none"
          >
            {submitting ? (
              <div className="flex items-center">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                <span className="ml-2">{translatedTexts['submittingText'] || 'Submitting...'}</span>
              </div>
            ) : (
              translatedTexts['submitButton'] || 'Submit Request'
            )}
          </button>
        </div>
        <div className="flex w-full items-center justify-center gap-2 py-4">
          <Label>{translatedTexts['poweredBy'] || 'Powered by'}</Label>
          <img alt="GoTrust" src={gotrustTitle} className="h-12 w-20 object-contain" />
        </div>
      </form>
    </div>
  );
}

export default RequestForm;
