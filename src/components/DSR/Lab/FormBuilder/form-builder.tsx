import { zodResolver } from '@hookform/resolvers/zod';
import { ColumnDef } from '@tanstack/react-table';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { Button } from '../../../../@/components/ui/Common/Elements/Button/Button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../../../../@/components/ui/Common/Elements/Dialog/Dialog';
import { Input } from '../../../../@/components/ui/Common/Elements/Input/Input';
import link from '../../../../assets/images/url.svg';
import plus from '../../../../assets/plusSign.svg';

import { ArrowUpDown, Globe } from 'lucide-react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogTitle,
} from '../../../../@/components/ui/AlertDialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../../../../@/components/ui/Common/Elements/Form/Form';
import { Label } from '../../../../@/components/ui/Common/Elements/Label/Label';
import {
  RadioGroup,
  RadioGroupItem,
} from '../../../../@/components/ui/Common/Elements/RadioGroup/radio-group';
import SearchableSelect from '../../../../@/components/ui/Common/Elements/Select/SearchableSelect';

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../../../../@/components/ui/tooltip';
import deleteIcon from '../../../../assets/delete.svg';
import Eye from '../../../../assets/IconoirEyeSolid.svg';
import { fetchEntities } from '../../../../redux/actions/ActivityLog/ActivityLogActions';
import { getRegulationsList } from '../../../../redux/actions/CustomerManagement/CustomerManagementActions';
import {
  setActiveFormBuilder,
  setAllCategoryFormBuilder,
  setCreateFormTitle,
  setDSRSelectedFormBuilderCategoryStep,
  setFormBuilderData,
  setResetAllCategoryFormBuilder,
} from '../../../../redux/reducers/DSR/dsr-slice';
import { AppDispatch, RootState } from '../../../../redux/store';
import { FormBuilderTable, FormBuilderType } from '../../../../types/data-subject-rights';
import {
  DSR_FORM_BUILDER_CREATE_FORM,
  DSR_FORM_BUILDER_VIEW,
  DSR_FORM_TRANSLATION,
  DSR_TASK_OVERVIEW,
} from '../../../../utils/routeConstant';
import { convertDateToHumanView } from '../../../common/CommonHelperFunctions';
import {
  createFormBuilder,
  deleteDSRFormBuilder,
  fetchFormBuilder,
} from '../../../common/services/data-subject-request';
import DynamicTable from '../../../common/ShadcnDynamicTable/dynamic-table';

// const schema = z.object({
//   name: z.string().min(1, 'This field is required'),
//   busi_unit_id: z.string().min(1, 'This field is required'),
//   regulation_id: z.array(z.number()).optional(),
//   enable_verification: z.boolean().default(false),
//   authentication_type: z
//     .string()
//     .optional()
//     .refine((value) => !value || value.length > 0, {
//       message: 'Authentication type is required when verification is enabled',
//       path: ['authentication_type'],
//     }),
// });

const schema = z.object({
  name: z.string().min(1, 'This field is required'),
  busi_unit_id: z.string().min(1, 'This field is required'),
  regulation_id: z.array(z.number()).optional(),
  enable_verification: z.boolean().default(true), // Default to true
  authentication_type: z
    .string()
    .min(1, 'Authentication type is required when verification is enabled'),
});

const FormBuilder: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const [isOpenCreateDialog, setIsOpenCreateDialog] = useState<boolean>(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [selectedRow, setSelectedRow] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tableData, setTableData] = useState<any[]>([]);
  const [isDeleting, setIsDeleting] = useState(false);
  const [customerRegulationsList, setCustomerRegulationsList] = useState<any[]>([]);
  const { entities, selectedEntityId } = useSelector((state: RootState) => state.activityLog);
  const loginData = useSelector((state: any) => state.auth.login.login_details);
  const customer_id = loginData?.customer_id ?? 0;
  const location = useLocation();

  const { t } = useTranslation();

  const formBuilderTableColumns: ColumnDef<FormBuilderTable>[] = [
    {
      accessorKey: 'formName',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('DSR.FormBuilder.FormName')} <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => <span>{row.getValue('formName')}</span>,
    },

    {
      accessorKey: 'published',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('DSR.FormBuilder.Published')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const rawValue = row.getValue('published');
        const isPublished = rawValue === true || rawValue === 'YES' || rawValue === 'Published';

        const status = isPublished ? 'Published' : 'Draft';
        const bgColor = status === 'Draft' ? '#CBD5E1' : '#2DB078';
        const textColor = status === 'Published' ? '#ffffff' : 'inherit';

        return (
          <span
            style={{
              backgroundColor: bgColor,
              color: textColor,
              padding: '4px 8px',
              borderRadius: '20px',
              display: 'inline-block',
            }}
          >
            {status}
          </span>
        );
      },
    },
    {
      accessorKey: 'busi_unit_id',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('CompanyStructure.CompanyTree.BusinessUnit')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => <span>{row.original.Group?.name || '-'}</span>,
    },
    {
      accessorKey: 'updatedAt',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('DSR.FormBuilder.LastUpdated')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const updatedAt: string = row.getValue('updatedAt');
        return (
          <div>{updatedAt ? convertDateToHumanView(updatedAt) : row.getValue('updatedAt')}</div>
        );
      },
    },
    {
      accessorKey: 'actionVersion',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('DSR.FormBuilder.ActionVersion')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const actionVersion = row.getValue('actionVersion');
        return (
          <span>
            {typeof actionVersion === 'string' || typeof actionVersion === 'number'
              ? actionVersion
              : '-'}
          </span>
        );
      },
    },
    {
      accessorKey: 'translations',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Translations
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const translations = row.getValue('translations') as any[];
        const rawValue = row.getValue('published');
        const isPublished = rawValue === true || rawValue === 'YES' || rawValue === 'Published';

        if (!translations || translations.length === 0) {
          return <div className="text-gray-500">-</div>;
        }

        const languageCount = translations.length;
        const languageLabel = (
          <div className="font-sm flex items-center gap-1 bg-transparent text-accent-foreground/70 hover:text-accent-foreground">
            <div className="w-2 text-lg">{languageCount}</div>
            <Globe className="h-[14%] w-[14%]" />
          </div>
        );

        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="w-32 cursor-pointer">
                  <span className="">{languageLabel}</span>
                </div>
              </TooltipTrigger>
              <TooltipContent className="relative max-w-sm rounded-lg border border-gray-200 bg-white p-0">
                <div className="p-4">
                  <div className="mb-3">
                    <div className="grid grid-cols-2 gap-2">
                      {translations.map((translation, index) => (
                        <span
                          key={index}
                          className="inline-block rounded-full bg-custom-primary px-2 py-1 text-xs text-white"
                        >
                          {translation.language}
                        </span>
                      ))}
                    </div>
                  </div>
                  {isPublished && (
                    <div className="border-t border-gray-200 pt-1">
                      <Button
                        variant="default"
                        size="sm"
                        className="w-full p-1 text-xs outline-none"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate(DSR_FORM_TRANSLATION, {
                            state: { formId: row.original.id },
                          });
                        }}
                      >
                        + Add Language
                      </Button>
                    </div>
                  )}
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      },
    },
    {
      accessorKey: 'url',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('DSR.FormBuilder.URL')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const url: string = row.getValue('url');
        const rawValue = row.getValue('published');
        const isPublished = rawValue === true || rawValue === 'YES' || rawValue === 'Published';

        const handleCopyUrl = (e: React.MouseEvent) => {
          e.stopPropagation();
          if (isPublished) {
            const currentLanguage = localStorage.getItem('language') || 'en';
            let finalUrl = url;
            try {
              if (url.includes('?')) {
                const [baseUrl, queryString] = url.split('?');
                finalUrl = `${baseUrl}?language_code=${currentLanguage}`;
              } else {
                finalUrl = `${url}?language_code=${currentLanguage}`;
              }
            } catch (error) {
              finalUrl = `${url}?language_code=${currentLanguage}`;
            }

            navigator.clipboard
              .writeText(finalUrl)
              .then(() => toast.success(t('ToastMessages.General.URLCopiedSuccessfully')))
              .catch(() => toast.error(t('FrontEndErrorMessage.FormBuilder.FailedToCopyURL')));
          } else {
            toast.error(t('FrontEndErrorMessage.FormBuilder.URLNotAvailableForDraftForms'));
          }
        };

        return (
          <Button
            variant="ghost"
            onClick={handleCopyUrl}
            className={`p-3 ${!isPublished ? 'cursor-not-allowed opacity-50' : ''}`}
          >
            <img src={link} alt="url" className="h-4 w-4" />
          </Button>
        );
      },
    },
    {
      accessorKey: 'action',
      header: () => (
        <Button variant="ghost" className="p-0">
          {t('DSR.FormBuilder.Action')}
        </Button>
      ),
      cell: ({ row }) => {
        const rawValue = row.getValue('published');
        const isPublished = rawValue === true || rawValue === 'YES' || rawValue === 'Published';

        return (
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              className="h-8 w-8 p-0"
              disabled={!isPublished}
              onClick={(e) => {
                e.stopPropagation();
                handleViewCodeSnippets(row.original);
              }}
            >
              <img src={Eye} alt="view" className="size-5" />{' '}
            </Button>

            <Button
              variant="default"
              className={`h-8 w-8 p-0 ${isPublished ? 'cursor-not-allowed opacity-50' : ''}`}
              onClick={(e) => {
                e.stopPropagation();
                if (!isPublished) {
                  handleDeleteRequest(row.original);
                }
              }}
              title={t('Common.Delete')}
            >
              <img src={deleteIcon} alt="delete" className="aspect-square h-4 w-4" />
            </Button>
          </div>
        );
      },
    },
  ];

  // const form = useForm<FormBuilderType>({
  //   resolver: zodResolver(schema),
  //   mode: 'all',
  //   defaultValues: {
  //     name: '',
  //     busi_unit_id: '',
  //     regulation_id: [],
  //     enable_verification: false,
  //     authentication_type: '',
  //   },
  // });

  const form = useForm<FormBuilderType>({
    resolver: zodResolver(schema),
    mode: 'all',
    defaultValues: {
      name: '',
      busi_unit_id: '',
      regulation_id: [],
      authentication_type: 'EMAIL', // Default to EMAIL
    },
  });

  // const verificationEnabled = form.watch('enable_verification');
  const verificationEnabled = true; // Always true

  useEffect(() => {
    const fromTaskOverview = location.state?.fromTaskOverview;
    if (fromTaskOverview) {
      setIsOpenCreateDialog(true);
    }
  }, [location]);

  useEffect(() => {
    if (!verificationEnabled) {
      form.setValue('authentication_type', '');
    }
  }, [verificationEnabled, form]);

  useEffect(() => {
    async function fetchRegulationList() {
      try {
        const data = await getRegulationsList();
        if (data && Array.isArray(data.rows)) {
          setCustomerRegulationsList(data.rows);
        } else {
          console.error('Expected "rows" to be an array in the API response.');
        }
      } catch (error) {
        console.error('Error fetching customer regulations list:', error);
      }
    }
    fetchRegulationList();
  }, []);

  const transformApiDataToTableFormat = (data: any[]) => {
    return data.map((item: any) => {
      // Get existing translations from API
      const apiTranslations = item?.DsrFormTranslations || [];

      // Check if English already exists in translations
      const hasEnglish = apiTranslations.some(
        (translation: any) =>
          translation.language?.toLowerCase() === 'english' ||
          translation.language_code?.toLowerCase() === 'en'
      );

      // Add English as default if it doesn't exist
      const translations = hasEnglish
        ? apiTranslations
        : [{ language: 'English', language_code: 'en' }, ...apiTranslations];

      return {
        id: item?.id,
        formName: item?.name,
        published: item?.published || item?.status || false,
        updatedAt: item?.updatedAt || '-',
        actionVersion: item?.version || null,
        url: item?.url || '',
        translations: translations,
        allCategoryFormBuilder: item?.categories || [],
        authentication_type: item?.authentication_type || '',
        regulation_id: item?.regulation_id || [],
        enable_verification: !!item.authentication_type,
        busi_unit_id: item?.busi_unit_id || '-',
        content: item?.content || null,
        Group: item?.Group || null,
      };
    });
  };

  useEffect(() => {
    const loadFormBuilderData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const data = await fetchFormBuilder();

        if (!Array.isArray(data)) {
          throw new Error('Invalid data format received from server');
        }

        const transformedData = transformApiDataToTableFormat(data);
        setTableData(transformedData);
      } catch (err: any) {
        setError(err.message);
        setTableData([]);
        toast.error(t('FrontEndErrorMessage.FormBuilder.FailedToLoadFormBuilderData'));
      } finally {
        setIsLoading(false);
      }
    };

    loadFormBuilderData();
  }, []);

  useEffect(() => {
    dispatch(fetchEntities(customer_id, selectedEntityId));
  }, [dispatch, customer_id, selectedEntityId]);

  useEffect(() => {
    dispatch(setResetAllCategoryFormBuilder());
  }, [dispatch]);

  const handleView = (row: any) => {
    const formBuilder = tableData?.find((item) => String(item.id) === String(row.id));
    if (formBuilder) {
      dispatch(setCreateFormTitle(formBuilder.formName));
      dispatch(setAllCategoryFormBuilder(formBuilder.allCategoryFormBuilder));
      dispatch(setFormBuilderData(formBuilder));
      dispatch(setActiveFormBuilder(Number(formBuilder.id)));
      dispatch(setDSRSelectedFormBuilderCategoryStep(0));

      navigate(DSR_FORM_BUILDER_CREATE_FORM, {
        state: {
          formData: formBuilder,
          contentData: formBuilder.content,
        },
      });
    } else {
      toast.error(t('FrontEndErrorMessage.FormBuilder.FormNotFound'));
    }
  };

  const handleViewCodeSnippets = (row: any) => {
    const formBuilder = tableData?.find((item) => String(item.id) === String(row.id));
    if (formBuilder) {
      navigate(DSR_FORM_BUILDER_VIEW, {
        state: {
          formData: formBuilder,
        },
      });
    } else {
      toast.error(t('FrontEndErrorMessage.FormBuilder.FormNotFound'));
    }
  };
  const handleCancle = () => {
    const fromTaskOverview = location.state?.fromTaskOverview;
    if (fromTaskOverview) {
      navigate(`${DSR_TASK_OVERVIEW}`);
    }
    form.reset();
    setIsOpenCreateDialog(false);
  };

  const handleDeleteRequest = (row: any) => {
    setSelectedRow(row);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (selectedRow) {
      setIsDeleting(true);
      try {
        const response = await deleteDSRFormBuilder(selectedRow.id);
        if (!response.success) {
          throw new Error(response.message || 'Failed to delete form');
        }
        const refreshedData = await fetchFormBuilder();
        if (Array.isArray(refreshedData)) {
          const transformedData = transformApiDataToTableFormat(refreshedData);
          setTableData(transformedData);
        }

        toast.success(
          response.message || t('FrontEndErrorMessage.FormBuilder.FormDeletedSuccessfully')
        );
      } catch (error: any) {
        const errorMessage =
          error.response?.data?.message ||
          error.message ||
          t('FrontEndErrorMessage.FormBuilder.FailedToDeleteForm');
        toast.error(errorMessage);
        console.error('Delete error:', error);
      } finally {
        setIsDeleting(false);
        setDeleteDialogOpen(false);
        setSelectedRow(null);
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setSelectedRow(null);
  };

  const onSubmit = async (data: FormBuilderType) => {
    setIsSubmitting(true);
    try {
      const payload: any = {
        name: data.name,
        busi_unit_id: data.busi_unit_id,
      };

      if (data.regulation_id && data.regulation_id.length > 0) {
        payload.regulation_id = data.regulation_id;
      }

      if (data.enable_verification && data.authentication_type) {
        payload.authentication_type = data.authentication_type;
      }

      await createFormBuilder(payload);

      const refreshedData = await fetchFormBuilder();
      if (Array.isArray(refreshedData)) {
        const transformedData = transformApiDataToTableFormat(refreshedData);
        setTableData(transformedData);
      }

      toast.success(t('ToastMessages.General.FormCreatedSuccessfully'));
      setIsOpenCreateDialog(false);
      form.reset();
    } catch (error: any) {
      toast.error(error.message || t('FrontEndErrorMessage.FormBuilder.FailedToCreateForm'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="size-full">
      <div className="rounded-md bg-white py-2">
        <div className="my-2 flex h-9 flex-row items-center justify-between gap-5 px-3">
          <div>
            <Label className="text-xl font-semibold">{t('DSR.FormBuilder.FormBuilder')}</Label>
          </div>
          <div className="my-2 mb-2 flex justify-end">
            <Button
              variant="ghost"
              className="btn-background-effect ml-2 bg-primary text-white hover:text-white"
              onClick={() => setIsOpenCreateDialog(true)}
            >
              <img src={plus} className="mr-2 text-white" title="Create Form" />{' '}
              {t('DSR.TaskOverView.CreateForm')}
            </Button>
            <AlertDialog open={isOpenCreateDialog} onOpenChange={setIsOpenCreateDialog}>
              <AlertDialogContent className="">
                <AlertDialogDescription>
                  <AlertDialogTitle className="text-2xl">
                    {t('DSR.TaskOverView.CreateForm')}
                  </AlertDialogTitle>
                </AlertDialogDescription>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t('DSR.FormBuilder.FormName')}
                            <span className="text-red-500">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="Enter form name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="busi_unit_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Business Unit
                            <span className="text-red-500">*</span>
                          </FormLabel>
                          <FormControl>
                            <SearchableSelect
                              style={{ width: '280px', height: '14px' }}
                              className={''}
                              options={[
                                {
                                  id: '-1',
                                  value: 'Select Entity',
                                },
                                ...(entities?.map((entity: any) => ({
                                  id: entity.id,
                                  value: entity.name,
                                })) ?? []),
                              ]}
                              value={field.value ? String(field.value) : ''}
                              onChange={field.onChange}
                              placeholder="Select"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="regulation_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('DSR.FormBuilder.Regulations')}</FormLabel>
                          <FormControl>
                            <SearchableSelect
                              style={{ width: '280px', height: '14px' }}
                              className={''}
                              childStyle="absolute bottom-0 mb-14"
                              options={[
                                {
                                  id: '-1',
                                  value: 'Select Regulations',
                                },
                                ...(customerRegulationsList?.map((entity: any) => ({
                                  id: entity.id,
                                  value: entity.authoritative_source,
                                })) ?? []),
                              ]}
                              value={field.value ? field.value.join(',') : ''}
                              onChange={(value) => {
                                const selectedValues = value.split(',').map(Number);
                                field.onChange(selectedValues);
                              }}
                              placeholder={t('DSR.WorkFlow.SelectRegulations')}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {/* <FormField
                      control={form.control}
                      name="enable_verification"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between">
                          <div className="space-y-0.5">
                            <FormLabel>
                              Do you want verifications?
                              {verificationEnabled && <span className="text-red-500"> *</span>}
                            </FormLabel>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    /> */}

                    {verificationEnabled && (
                      <FormField
                        control={form.control}
                        name="authentication_type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t('DSR.FormBuilder.VerificationMethod')}
                              <span className="text-red-500">*</span>
                            </FormLabel>
                            <FormControl>
                              <RadioGroup
                                className="flex flex-row"
                                value={field.value}
                                onValueChange={field.onChange}
                              >
                                {/* Comment out SMS and WhatsApp options */}
                                {/* <div className="flex items-center space-x-2">
              <RadioGroupItem value="SMS" id="r1" />
              <Label htmlFor="r1">SMS</Label>
            </div> */}
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value="EMAIL" id="r2" />
                                  <Label htmlFor="r2">{t('DSR.FormBuilder.Email')}</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value="WHATSAPP" id="r3" />
                                  <Label htmlFor="r3">WhatsApp</Label>
                                </div>
                              </RadioGroup>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    <AlertDialogFooter className="flex justify-end">
                      <Button type="button" variant="outline" onClick={handleCancle}>
                        {t('Common.Cancel')}
                      </Button>
                      <Button
                        type="submit"
                        className="btn-background-effect theme-hover-effect bg-custom-primary text-white hover:bg-custom-primary"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? t('Common.Submitting') : t('Common.Submit')}
                      </Button>
                    </AlertDialogFooter>
                  </form>
                </Form>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>

        <Dialog open={deleteDialogOpen} onOpenChange={handleCloseDeleteDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="text-2xl">Confirm Delete</DialogTitle>
              <DialogDescription className="pt-4 text-xl">
                {t('DSR.FormBuilder.ConfirmDeleteForm')}
                <span className="font-semibold"> {selectedRow?.formName}</span>{' '}
                {t('DSR.FormBuilder.Form')}.
              </DialogDescription>
            </DialogHeader>

            <DialogFooter className="flex justify-end gap-1">
              <Button variant="outline" onClick={handleCloseDeleteDialog}>
                {t('Common.Cancel')}
              </Button>
              <Button
                className="ml-3 bg-custom-primary text-white hover:bg-custom-primary hover:text-white"
                onClick={confirmDelete}
                disabled={isDeleting}
              >
                {isDeleting ? t('Common.Deleting...') : t('Common.Delete')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      <DynamicTable
        data={tableData}
        columns={formBuilderTableColumns}
        onRowClick={(row) => handleView(row)}
        enableSorting
        enablePagination
      />
    </div>
  );
};

export default FormBuilder;
