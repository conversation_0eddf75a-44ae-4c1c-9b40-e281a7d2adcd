import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, Download, MessagesSquare } from 'lucide-react';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useLocation, useNavigate } from 'react-router-dom';
import { Tooltip } from 'react-tooltip';

import { Button } from '../../../../../@/components/ui/Common/Elements/Button/Button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../../@/components/ui/Common/Elements/Select/Select';
import {
  Tabs,
  TabsList,
  TabsTrigger,
} from '../../../../../@/components/ui/Common/Elements/Tabs/Tabs';
import attachment from '../../../../../assets/attachment.svg';
import chatIcon from '../../../../../assets/chatIcon.svg';
import greyCircle from '../../../../../assets/GreyEllipse.svg';
import multiPerson from '../../../../../assets/Multiple people icon.svg';
import { DsrReduxTaskDataItem } from '../../../../../types/data-subject-rights';
import { getInitialsByName } from '../../../../../utils/helperData';
import AvatarFrame from '../../../../common/Avatar';
import { convertDateToHumanView } from '../../../../common/CommonHelperFunctions';
import { useStepper } from '../../../../common/Stepper';
import TaskDataTable from '../../../common/table/dsr-data-table';
import AddTaskModalTaskoverview from '../../../Lab/Common/Modal/add-task-modal-taskoverview';
import ChatView from '../chat-view';
import styles from './acknowledgment.module.css';

import { Maximize2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '../../../../../@/components/ui/Common/Elements/Accordian/Accordian';
import { Card } from '../../../../../@/components/ui/Common/Elements/Card/Card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTitle,
} from '../../../../../@/components/ui/Common/Elements/Dialog/Dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from '../../../../../@/components/ui/Common/Elements/Form/Form';
import { Textarea } from '../../../../../@/components/ui/textarea';
import { ApprovedRowData } from '../../../../../types/data-subject-rights';
import {
  DSR_TASK_OVERVIEW,
  DSR_TASK_OVERVIEW_APPROVED,
  DSR_TASK_OVERVIEW_COMPLETED,
  DSR_TASK_OVERVIEW_REJECT_IN_PROGRESS,
} from '../../../../../utils/routeConstant';
import { TASK_OVERVIEW, UPDATE_TASK_PROGRESS } from '../../../../common/api';
import {
  addTaskOverview,
  approveRejectedRequest,
  downloadAuditLog,
  fetchAllAudit,
  fetchChatCount,
  SaveAprovedReqeuestSteps,
  updateTaskOverview,
} from '../../../../common/services/data-subject-request';
import { CardContent } from '../../../../UCF/Dashboard/common/ui/card';
import Modal from '../../../../UI/Modal';
import CompletionModals from '../../../Lab/Common/Modal/complete-flow-modal';
import SuccessModal from '../../../Lab/Common/Modal/success-modal';
import ConfirmationDialog from '../confirmation-modal';
import ExpandablePanel from '../ExpandableLog';
import PreviewCreateRequestForm from './preview-request-form';
interface ApprovedData {
  dataDetails: ApprovedRowData | undefined;
  details: any;
  auditLogData: {
    result: {
      id: number;
      action: string;
      createdAt: string;
      User: {
        firstName: string;
        lastName: string;
      };
    }[];
  };
  handleTaskDataChangeStep: (activeStepIndex: number) => void;
  departments: Department[];
}

type Department = {
  id: number;
  name: string;
  parent_id: number | null;
  customer_id: number;
  spoc_id: number;
};

interface TaskTable {
  title?: string;
  assigned_to?: string;
  created_date?: Date | string;
  end_date?: Date | string;
  department?: string;
  progress?: string;
  steps?: string;
  priority?: string;
  attachment?: number;
}
interface AddTaskModalTaskOverViewFormData {
  id?: number;
  stage_id: number | undefined;
  title: string;
  department_id: string;
  start_date: string | undefined;
  due_date: string | undefined;
  guidance_text: string;
  assignee_id: string[];
  TaskDocuments: AddTaskModalTaskOverViewUploadedFile[];
  requirement: string;
  documents?: [];
  step: number;
}

interface AddTaskModalTaskOverViewUploadedFile {
  id: string;
  file: File;
  original_name: string;
  size: number;
  type: string;
}
interface User {
  firstName: string;
  lastName: string;
}

interface PathData {
  progress: string;
  stage_id?: number;
  request_id?: number;
}

const Acknowledgment: React.FC<ApprovedData> = ({
  dataDetails,
  details,
  auditLogData,
  handleTaskDataChangeStep,
  departments,
}) => {
  const [addTaskModalOpen, setAddTaskModalOpen] = useState(false);
  const [taskTableData, setTaskTable] = useState(details.tasks);
  const location = useLocation();
  const { data } = location?.state || {};
  const [id, seRequestId] = useState(location.state.data.id);
  const {
    nextStep,
    prevStep,
    previousActiveStep,
    currentStep,
    activeStep,
    initialStep,
    isLastStep,
    setStep,
  } = useStepper();
  const [activeTab, setActiveTab] = useState('0');
  const [showChatView, setShowChatView] = useState<boolean>(false);

  const [showTaskView, setShowTaskView] = useState<boolean>(false);
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [isAuditLogPanelOpen, setIsAuditLogPanelOpen] = useState(false);
  const [auditLogs, setAuditLogs] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const userRole = useSelector((state: any) => state.auth.login.login_details);
  const [showPendingWarningModal, setShowPendingWarningModal] = useState(false);
  const [isRejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [isDocumentSharedOpen, setIsDocumentSharedOpen] = useState(false);
  const [isConfirmCompleteOpen, setIsConfirmCompleteOpen] = useState(false);
  const [active_index, setActiveIndex] = useState(0);
  const [path_data, setPathData] = useState({ progress: '' });
  const [task_id, setTask_id] = useState(0);
  const handleOpenAuditLogPanel = () => setIsAuditLogPanelOpen(true);
  const handleAuditLogClosePanel = () => setIsAuditLogPanelOpen(false);
  const { t } = useTranslation();
  const [unreadCount, setUnreadCount] = useState(0);

  //* Tasks
  const TaskDataTableColumns: ColumnDef<DsrReduxTaskDataItem>[] = [
    {
      accessorKey: 'title',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            {t('DSR.TaskOverView.TasksTitle')}
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const title: string = row.getValue('title');

        return <div className="">{`${title || '-'}`}</div>;
      },
    },
    {
      accessorKey: 'assignee_id',
      header: ({ column }: any) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('DSR.TaskOverView.Assign')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }: any) => {
        const assigned_to: User[] = row.original?.users || [];
        const a = assigned_to.map((item: any) => `${item.firstName} ${item.lastName}`).join(', ');
        const initial =
          assigned_to.length > 0
            ? `${assigned_to[0]?.firstName[0] ?? ''} ${assigned_to[0]?.lastName[0] ?? ''}`
            : '';
        const fullName =
          assigned_to.length > 0
            ? `${assigned_to[0]?.firstName ?? ''} ${assigned_to[0]?.lastName ?? ''}`
            : '';
        return (
          <div className="flex flex-row items-center gap-2 whitespace-nowrap">
            {assigned_to.length > 0 ? (
              <>
                {assigned_to.length > 1 ? (
                  <div className="flex-start relative flex items-center">
                    <AvatarFrame value={initial} />
                    <img
                      id={`anchor_elements_${row.id}`}
                      src={multiPerson}
                      alt="Multiple People Logo"
                      className="absolute -bottom-px left-8 aspect-square h-10"
                    />
                    <Tooltip
                      anchorSelect={`#anchor_elements_${row.id}`}
                      style={{
                        color: '#FFF',
                        fontFamily: 'Poppins',
                        fontSize: '14px',
                        fontStyle: 'normal',
                        fontWeight: '400',
                        lineHeight: 'normal',
                        borderRadius: '8px',
                        backgroundColor: '#000',
                        opacity: '0.8',
                        display: 'inline-flex',
                        padding: '9px 16px',
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                        gap: '10px',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                      }}
                      content={a}
                    />
                  </div>
                ) : (
                  <>
                    <AvatarFrame value={fullName} getInitials={getInitialsByName} />
                    {fullName}
                  </>
                )}
              </>
            ) : (
              <>-</>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'start_date',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            {t('DSR.WorkFlow.StartDate')}
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const start_date: string | number | Date = row.getValue('start_date');
        return (
          <div className="">{start_date ? convertDateToHumanView(start_date.toString()) : '-'}</div>
        );
      },
    },
    {
      accessorKey: 'due_date',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            {t('DSR.WorkFlow.DueDate')}
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const due_date: string | number | Date = row.getValue('due_date');
        return (
          <div className="">{due_date ? convertDateToHumanView(due_date.toString()) : '-'}</div>
        );
      },
    },
    {
      accessorKey: 'department',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            {t('VendorRiskManagement.CreateNewVendor.department')}
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const departmentName = row.original?.Department?.name;
        return <div className="">{departmentName || '-'}</div>;
      },
    },
    {
      accessorKey: 'progress',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            {t('Ropa.Activity.TableHeading.progress')}
            <ArrowUpDown className="size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const progress: string = row.getValue('progress');
        const isDisabled =
          dataDetails?.status === 'ARCHIVED' || dataDetails?.status === 'COMPLETED';

        const getColor = (value: string) => {
          switch (value) {
            case 'NOT_STARTED':
              return 'border-[#606060] text-[#606060] bg-[#3333331A]';
            case 'IN_PROGRESS':
              return 'border-[#DE9609] text-[#DE9609] bg-[#FFC1461A]';
            case 'COMPLETED':
              return 'border-[#2DB079] text-[#2DB079] bg-[#05CF781A]';
            case 'REJECTED':
              return 'border-[#ff0000] text-[#ff0000]  bg-[#FFE5E5]';
            default:
              return 'text-gray-500';
          }
        };
        return (
          <Select
            defaultValue={progress}
            disabled={isDisabled}
            onValueChange={(value: string) => {
              const patcData = { progress: value, request_id: details.id };
              checkLastStep(activeStep, patcData, row.original.id);
            }}
          >
            <SelectTrigger
              className={`flex w-[110px] items-center border-transparent bg-transparent p-0 focus:border-transparent focus:ring-0 focus:ring-transparent ${isDisabled ? 'cursor-not-allowed opacity-50' : ''}`}
            >
              <span className={`rounded-md border p-1 ${getColor(progress)}`}>
                <SelectValue placeholder="Theme" />
              </span>
            </SelectTrigger>
            <SelectContent className="border-none outline-none ring-0">
              <SelectItem
                value="NOT_STARTED"
                className="cursor-pointer hover:bg-gray-100 focus:bg-gray-100"
              >
                {t('DSR.TaskOverView.NotStarted')}
              </SelectItem>
              <SelectItem
                value="IN_PROGRESS"
                className="cursor-pointer hover:bg-gray-100 focus:bg-gray-100"
              >
                {t('DSR.TaskOverView.InProgress')}
              </SelectItem>
              <SelectItem
                value="COMPLETED"
                className="cursor-pointer hover:bg-gray-100 focus:bg-gray-100"
              >
                {t('DSR.Dashboard.Completed')}
              </SelectItem>
              <SelectItem
                value="REJECTED"
                className="cursor-pointer text-[#ff0000] hover:bg-gray-100 hover:text-[#ff0000] focus:bg-gray-100 focus:text-[#ff0000]"
              >
                {t('DSR.TaskOverView.RejectTask')}
              </SelectItem>
            </SelectContent>
          </Select>
        );
      },
    },
    {
      accessorKey: 'steps',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            {t('DSR.TaskOverView.Steps')}
            <ArrowUpDown className="ml-2 size-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const stepTitle = row.original?.RequestTypeStage?.step_title;
        return <div className="">{stepTitle || '-'}</div>;
      },
    },
    {
      accessorKey: 'attachment',
      header: () => {
        return (
          <Button variant="ghost" className="p-0">
            {t('DSR.TaskOverView.Attachment')}
          </Button>
        );
      },
      cell: ({ row }) => {
        const attachments = row.original?.TaskDocuments || [];
        const numberOfAttachments = attachments.length;

        return (
          <Button variant="default" className="flex flex-row gap-2">
            {numberOfAttachments > 0 && (
              <img src={attachment} alt="Attachment Icon" className="aspect-square h-6" />
            )}
            <span>{numberOfAttachments > 0 ? `${numberOfAttachments} Attachments` : '-'}</span>
          </Button>
        );
      },
    },
  ];

  const handleAddTask = async (data: AddTaskModalTaskOverViewFormData) => {
    toast.loading(t('FrontEndErrorMessage.DSR.AddingTask'));
    const workflow_id = dataDetails?.DsrRequestType?.id;
    const stage_id = details?.steps[activeStep].id;
    const request_id = dataDetails?.id; //Reuest id
    // const stage_id = stepData[activeStep].id
    // if(data?.documents?.length){

    // }
    if (data?.documents?.length == 0) delete data?.documents;
    // if(data?.TaskDocuments?.length == 0) delete data?.TaskDocuments
    const updatedData = {
      ...data,
      workflow_id: workflow_id,
      stage_id: stage_id,
      request_id: request_id,
      assignee_id: data?.assignee_id,
    };
    // console.log("test0000",updatedData)
    if (updatedData?.id != undefined) delete updatedData?.id;
    try {
      const response = await addTaskOverview(updatedData);
      if (response.success) {
        toast.dismiss();
        toast.success(t('ToastMessages.Workflow.TaskAddedSuccessfully'));
        const taskss = {
          title: response?.result?.title,
          guidance_text: response?.result?.guidance_text,
          assigned_to: '',
          created_date: '',
          end_date: '',
          attachment: 0,
          department: '',
          priority: '',
          requirement: '',
          step: '',
        };
        handleTaskDataChangeStep(stage_id);
        // setDsrAddWorkflowTableData(updatedDsrAddTaskArray);
        setAddTaskModalOpen(false);
      } else {
        toast.dismiss();
        toast.error('Failed to add task');
      }
    } catch (error) {
      toast.dismiss();
      toast.error('Error while adding task');
    }
  };

  // update request
  const saveData = async (activeStepIndex: number, pathData: {}) => {
    const workflow_step_id =
      details?.steps?.length > 0 ? details?.steps[activeStep]?.id : undefined;
    const url = `${TASK_OVERVIEW}/${details.id}`;
    try {
      const step = details?.steps[activeStep];
      const requestdata = await SaveAprovedReqeuestSteps(url, pathData, step);
      if (requestdata?.success) {
        handleTaskDataChangeStep(details?.steps[activeStep + 1].id);
        setActiveTab('1');
        toast.dismiss();
        toast.success(t('ToastMessages.General.Success'));
      }
    } catch (error) {
      console.error('Error setting chart data:', error);
    }
  };
  const checkLastStep = (activeStepIndex: number, pathData: PathData, task_id: number) => {
    const updatedTasks = taskTableData.map((task: { id: number }) =>
      task.id === task_id ? { ...task, progress: pathData.progress } : task
    );
    const allTasksCompleted = updatedTasks.find((task: any) => task.progress !== 'COMPLETED');

    if (isLastStep && !allTasksCompleted) {
      const flowtype = dataDetails?.DsrRequestType?.flowtype;
      if (flowtype?.toLowerCase().includes('access')) {
        setIsDocumentSharedOpen(true);
        setActiveIndex(activeStepIndex);
        setPathData(pathData);
        setTask_id(task_id);
      } else {
        // handleRequestComplete(activeStepIndex)
        setIsConfirmCompleteOpen(true);
        setActiveIndex(activeStepIndex);
        setPathData(pathData);
        setTask_id(task_id);
      }
    } else {
      updateProgress(activeStepIndex, pathData, task_id);
    }
  };
  // update task
  const updateProgress = async (activeStepIndex: number, pathData: PathData, task_id: number) => {
    const workflow_step_id =
      details?.steps?.length > 0 ? details?.steps[activeStep]?.id : undefined;
    const url = `${UPDATE_TASK_PROGRESS}/${task_id}`;
    const tempPathData = {
      ...pathData,
      stage_id: workflow_step_id,
    };
    try {
      const step = workflow_step_id;
      const requestdata = await SaveAprovedReqeuestSteps(url, tempPathData, step);
      if (requestdata?.success) {
        handleTaskDataChangeStep(details?.steps[activeStep].id);
        setActiveTab('1');
        toast.success(t('ToastMessages.General.Success'));

        const updatedTasks = taskTableData.map((task: { id: number }) =>
          task.id === task_id ? { ...task, progress: pathData.progress } : task
        );
        const allTasksCompleted = updatedTasks.every((task: any) => task.progress === 'COMPLETED');

        if (allTasksCompleted && !isLastStep) {
          handleNextStep();
        }
      }
    } catch (error) {
      console.error('Error setting chart data:', error);
    }
  };

  useEffect(() => {
    const stepIndex = details.steps.findIndex((row: any) => row.id == details.workflow_step_id);
    // console.log("test603==>stepIndex",stepIndex, details.workflow_step_id)
    // setStep(stepIndex)
    setTaskTable(details.tasks), setActiveTab('1');
  }, [details]);
  const form = useForm({
    defaultValues: {
      reject_reason: dataDetails?.reject_reason || '',
    },
  });

  //Update Task
  const handleUpdateTask = async (data: AddTaskModalTaskOverViewFormData) => {
    try {
      toast.loading(t('CommonErrorMessages.UpdatingTask'));

      const workflow_id = dataDetails?.DsrRequestType?.id;
      const requirement = details?.DsrRequestType?.requirement;
      const stage_id = details?.steps[activeStep]?.id;
      const request_id = dataDetails?.id;

      const updatedData = {
        ...data,
        workflow_id,
        stage_id,
        request_id,
        requirement,
        assignee_id: data?.assignee_id,
        step: data?.step,
      };

      if (updatedData?.documents?.length === 0) {
        delete updatedData.documents;
      }

      const taskId = data?.id as number;
      const response = await updateTaskOverview(updatedData, taskId);

      toast.dismiss();

      if (response.success) {
        toast.success(t('ToastMessages.Workflow.TaskUpdatedSuccessfully'));
        handleTaskDataChangeStep(stage_id);
        setAddTaskModalOpen(false);
      } else {
        toast.error('Failed to update task');
      }
    } catch (error) {
      toast.dismiss();
      toast.error('Error while updating task');
      console.error('Update Task Error:', error);
    }
  };

  const handleUpdate = (data: any) => {
    handleUpdateTask(data);
    setShowTaskView(false);
  };

  const handleClosePanel = () => {
    setIsPanelOpen(false);
  };

  const handleDownloadAuditLog = async () => {
    const request_id = dataDetails?.id;
    // console.log('Request', request_id);

    const toastId = toast.loading(t('CommonErrorMessages.DownloadingAuditLog'));

    try {
      toast.dismiss();
      toast.loading(t('CommonErrorMessages.ProcessingEllipsis'));
      const response = await downloadAuditLog(request_id);

      if (response.status) {
        toast.dismiss();
        setIsSuccessModalOpen(true);
      }
    } catch (error: any) {
      console.log(error);
      toast.dismiss();
      toast.error(error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (activeTab === '2') {
      const fetchAuditData = async () => {
        setIsLoading(true);
        console.log('Fetching audit logs for ID:', id);
        try {
          const data = await fetchAllAudit(id);
          console.log('Fetched audit logs:', data);
          setAuditLogs(data || {});
        } catch (error) {
          console.error('Error fetching audit logs:', error);
        } finally {
          setIsLoading(false);
          console.log('Audit log fetch operation completed');
        }
      };
      fetchAuditData();
    }
  }, [activeTab, id]);

  const handlePreviousActiveStep = () => {
    if (activeStep > 0) {
      prevStep();
      const previousStepId = details?.steps[activeStep - 1]?.id;

      if (previousStepId) {
        handleTaskDataChangeStep(previousStepId);
      }
      setActiveTab('1');
    }
  };

  const handleNextStep = () => {
    if (dataDetails?.status === 'PENDING') {
      setShowPendingWarningModal(true);
      return;
    }

    if (!isLastStep) {
      nextStep();
      const workflow_step_id =
        details?.steps?.length > 0 ? details?.steps[activeStep + 1]?.id : undefined;
      const patchData = { workflow_step_id: workflow_step_id };
      saveData(activeStep, patchData);
      setActiveTab('1');
    }
  };

  const handleRequestComplete = async () => {
    if (
      dataDetails?.status === 'APPROVED' &&
      dataDetails?.DsrRequestType?.flowtype?.toLowerCase().includes('access')
    ) {
      setIsDocumentSharedOpen(true);
    } else {
      setIsConfirmCompleteOpen(true);
    }
  };

  const handleCompleteConfirm = async (
    active_index: number,
    path_data: PathData,
    task_id: number
  ) => {
    try {
      setLoading(true);
      const reject_reason = 'reject_reason';
      const result = await approveRejectedRequest('COMPLETED', id, reject_reason);
      if (result.success) {
        toast.success(t('ToastMessages.General.RequestCompletedSuccessfully'));
        updateProgress(active_index, path_data, task_id);
        if (userRole?.role === 'Data Protection Officer') {
          navigate(DSR_TASK_OVERVIEW_COMPLETED);
        } else {
          navigate(DSR_TASK_OVERVIEW);
        }
      }
    } catch (error) {
      console.error('Error completing request:', error);
      toast.error(t('FrontEndErrorMessage.DSR.FailedToCompleteRequest'));
    } finally {
      setLoading(false);
      setIsConfirmCompleteOpen(false);
    }
  };

  const handleRejectInProgressSubmit = async () => {
    try {
      setLoading(true);
      const reject_reason = form.getValues('reject_reason');
      const result = await approveRejectedRequest('REJECTED_IN_PROGRESS', id, reject_reason);

      if (result.success) {
        toast.success(t('ToastMessages.DSR.RequestRejectedSuccessfully'));
        if (userRole?.role === 'Data Protection Officer') {
          navigate(DSR_TASK_OVERVIEW_REJECT_IN_PROGRESS);
        } else {
          navigate(DSR_TASK_OVERVIEW);
        }
      }
    } catch (error) {
      console.error('Error rejecting request:', error);
      toast.error('Failed to reject request');
    } finally {
      setLoading(false);
      form.reset();
      setRejectDialogOpen(false);
    }
  };

  const handleRequestSubmit = async () => {
    try {
      setLoading(true);
      const reject_reason = 'reject_reason';
      const result = await approveRejectedRequest('APPROVED', id, reject_reason);
      if (result.success) {
        toast.success(t('ToastMessages.DSR.RequestApprovedSuccessfully'));
        if (!isLastStep) {
          nextStep();
          const workflow_step_id =
            details?.steps?.length > 0 ? details?.steps[activeStep + 1]?.id : undefined;
          const patchData = { workflow_step_id: workflow_step_id };
          saveData(activeStep, patchData);
          setActiveTab('1');
        }
        if (userRole?.role === 'Data Protection Officer') {
          navigate(DSR_TASK_OVERVIEW_APPROVED);
        } else {
          navigate(DSR_TASK_OVERVIEW);
        }
      }
    } catch (error) {
      console.error('Error approving request:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCloseWarningModal = () => {
    setShowPendingWarningModal(false);
  };

  useEffect(() => {
    if (dataDetails?.business_unit) {
      console.log('Business unit is set:', dataDetails.business_unit);
    }
  }, [dataDetails?.business_unit]);

  useEffect(() => {
    const tasksWithDueDates = details?.tasks?.map(
      (task: { start_date: string | number | Date; due_days: number; due_date: any }) => {
        if (task.start_date && task.due_days && !task.due_date) {
          const startDate = new Date(task.start_date);
          const dueDate = new Date(startDate);
          dueDate.setDate(startDate.getDate() + task.due_days);
          return {
            ...task,
            due_date: dueDate.toISOString().split('T')[0],
          };
        }
        return task;
      }
    );

    setTaskTable(tasksWithDueDates);
    setActiveTab('1');
  }, [details]);

  const handleChatIconClick = async () => {
    try {
      // Call the API to update view count
      await fetchChatCount(id);
      // Reset the unread count locally
      setUnreadCount(0);
      // Open the chat view
      setShowChatView(true);
    } catch (error) {
      console.error('Error updating chat view count:', error);
      // Still open the chat view even if the API call fails
      setShowChatView(true);
    }
  };

  return (
    <div
      className="w-full pt-3 font-primary-text"
      style={{
        height: 'calc(100vh - 150px)',
      }}
    >
      <CompletionModals
        isDocumentSharedOpen={isDocumentSharedOpen}
        setIsDocumentSharedOpen={setIsDocumentSharedOpen}
        isConfirmCompleteOpen={isConfirmCompleteOpen}
        setIsConfirmCompleteOpen={setIsConfirmCompleteOpen}
        onComplete={() => handleCompleteConfirm(active_index, path_data, task_id)}
        isLoading={isLoading}
      />
      <Dialog open={isRejectDialogOpen} onOpenChange={setRejectDialogOpen}>
        <DialogContent className="flex min-h-[210px] flex-col">
          <DialogTitle>{t('DSR.TaskOverView.AddNote')}</DialogTitle>
          <DialogDescription className="w-full">
            <Form {...form}>
              <form className="space-y-4">
                <FormField
                  control={form.control}
                  name="reject_reason"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Textarea placeholder="Enter rejection note..." {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </form>
            </Form>
          </DialogDescription>
          <DialogFooter className="flex justify-end">
            <Button
              type="button"
              className="w-[95px] p-3 text-custom-primary outline outline-1 outline-custom-primary"
              onClick={() => setRejectDialogOpen(false)}
            >
              {t('Common.Cancel')}
            </Button>
            <Button
              type="button"
              className="w-[95px] bg-custom-primary p-3 text-primary-background hover:bg-custom-primary"
              onClick={handleRejectInProgressSubmit}
            >
              {t('DSR.TaskOverView.Reject')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <ConfirmationDialog
        isOpen={showPendingWarningModal}
        onClose={handleCloseWarningModal}
        onConfirm={handleCloseWarningModal}
        title="Cannot Proceed"
        description="Would you like to update the status of this request?"
        onApprove={handleRequestSubmit}
        onReject={() => {
          setRejectDialogOpen(true);
        }}
      />

      {isSuccessModalOpen && (
        <Modal
          open={isSuccessModalOpen}
          onClose={() => setIsSuccessModalOpen(false)}
          cssClass="w-[23.19%] shrink-0 rounded-2xl bg-white shadow-[10px_20px_30px_0px_rgba(0,0,0,0.10)]"
        >
          <SuccessModal onClose={() => setIsSuccessModalOpen(false)} />
        </Modal>
      )}
      {/* Expandable Panel Activity Log*/}
      <div>
        <ExpandablePanel isOpen={isPanelOpen} onClose={handleClosePanel} title="Form Preview">
          <div className="flex flex-col items-center">
            <div className="min-w-[75vw] rounded-md border border-gray-300">
              <PreviewCreateRequestForm />
            </div>
          </div>
        </ExpandablePanel>
      </div>

      {/* Expandable Panel Audit Log*/}
      <div>
        <ExpandablePanel
          isOpen={isAuditLogPanelOpen}
          onClose={handleAuditLogClosePanel}
          title="Audit Log"
        >
          {activeTab === '2' && (
            <div className="flex min-h-screen justify-center">
              <div className="min-w-[75vw] p-2">
                <div className="flex justify-end p-2">
                  <Button variant="outline" onClick={() => handleDownloadAuditLog()}>
                    {t('DSR.TaskOverView.DownloadAuditLog')}
                    <Download className="ml-2 h-4 w-4" />
                  </Button>
                </div>
                <Card className="min-h-[80vh] p-3">
                  <CardContent>
                    {isLoading ? (
                      <p>Loading...</p>
                    ) : Object.keys(auditLogs).length ? (
                      <Accordion type="single" collapsible defaultValue="item-0">
                        {Object.entries(auditLogs).map(([step, logs], index) => (
                          <AccordionItem key={index} value={`item-${index}`}>
                            <AccordionTrigger>{step}</AccordionTrigger>
                            <AccordionContent>
                              {logs.map((log: any, logIndex: any) => {
                                const isChatAction = log?.action?.toLowerCase().includes('chat');

                                return (
                                  <div
                                    key={`1`}
                                    className="flex w-full flex-row items-center gap-3 p-3"
                                  >
                                    <div className="relative w-full">
                                      {isChatAction && (
                                        <div className="absolute -top-2 left-[-8] z-10 bg-white p-1">
                                          <MessagesSquare size={20} className="text-blue-500" />
                                        </div>
                                      )}
                                      <div
                                        className={`flex min-h-[6vh] w-full cursor-default flex-row items-center justify-between rounded-lg border border-[#C0CDE0] bg-white p-4 text-base leading-normal text-[#999]`}
                                      >
                                        <div className="flex items-center gap-1">
                                          <AvatarFrame
                                            value={`${log?.User?.firstName}${log?.User?.lastName ? ` ${log?.User?.lastName}` : ''}`}
                                            getInitials={getInitialsByName}
                                          />
                                          <div className="flex flex-col">
                                            <span className="font-medium">
                                              {log?.User?.firstName}
                                            </span>
                                          </div>
                                        </div>
                                        <p className="w-3/5 font-medium">
                                          {log?.action
                                            ?.replace(/_/g, ' ')
                                            .replace(/\b\w/g, (letter: string) => letter)}
                                        </p>
                                        <div className="flex flex-row justify-between gap-2">
                                          <div className="flex w-1/2 items-center justify-center gap-2.5 whitespace-nowrap p-2.5">
                                            <img src={greyCircle} alt="Grey Circle Dot" />
                                            {convertDateToHumanView(log?.createdAt)}
                                          </div>
                                          <div className="flex w-1/2 items-center justify-center gap-2.5 whitespace-nowrap p-2.5">
                                            <img src={greyCircle} alt="Grey Circle Dot" />
                                            {new Date(log?.createdAt).toLocaleTimeString('en-US', {
                                              hour: '2-digit',
                                              minute: '2-digit',
                                            })}
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                );
                              })}
                            </AccordionContent>
                          </AccordionItem>
                        ))}
                      </Accordion>
                    ) : (
                      <p className="mt-4 text-center">No audit logs available.</p>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </ExpandablePanel>
      </div>

      <ChatView
        showChatView={showChatView}
        setShowChatView={setShowChatView}
        unreadCount={unreadCount}
        setUnreadCount={setUnreadCount}
        onChatClick={handleChatIconClick}
      />
      <div className="rounded-lg bg-[#f5f5f5] p-2">
        <div className="fex-wrap flex flex-row items-center justify-between rounded-lg pt-1">
          <div className="w-[200px] items-center rounded-md border border-slate-300 px-2 py-1">
            <Tabs
              value={activeTab}
              onValueChange={(currentTab) => setActiveTab(currentTab)}
              // onValueChange={(currentStep) => {
              //   dispatch(setCurrentActiveTabIndex(Number(currentStep)));
              //   setStep(Number(currentStep));
              // }}
              // className="w-full"
            >
              <TabsList className="flex flex-row items-center">
                {/* <Button variant="ghost" className="p-0">
                  <TabsTrigger
                    value={'0'}
                    // className={`${activeStep === 0 ? 'bg-[#FFFFFF]' : ''}`}
                  >
                    Attachments
                  </TabsTrigger>
                </Button> */}
                <Button
                  variant="ghost"
                  // disabled={isNextStepDisabled}
                  className="p-0"
                >
                  <TabsTrigger
                    value={'1'}
                    // className={`${activeStep === 1 ? 'bg-[#FFFFFF]' : ''}`}
                  >
                    Task
                  </TabsTrigger>
                </Button>
                <Button
                  variant="ghost"
                  // disabled={isNextStepDisabled}
                  className="p-0"
                >
                  <TabsTrigger
                    value={'2'}
                    // className={`${activeStep === 2 ? 'bg-[#FFFFFF]' : ''}`}
                  >
                    Audit Log
                  </TabsTrigger>
                </Button>
              </TabsList>
            </Tabs>
          </div>
          <div className="flex flex-row justify-end gap-2">
            {dataDetails?.status !== 'ARCHIVED' && (
              <Button
                className="relative flex h-9 w-9 items-center justify-center rounded-full bg-[#C0CDE0] p-0 text-blue-800 shadow-md transition-colors duration-200 hover:bg-[#A9BCD4]"
                onClick={handleChatIconClick}
                id="ancho"
              >
                <div className="flex h-full w-full items-center justify-center">
                  <img src={chatIcon} alt="chat" className="h-6 w-6" />
                </div>
                {unreadCount > 0 && (
                  <div className="absolute -right-1 -top-1 flex h-5 min-w-[1rem] animate-pulse items-center justify-center rounded-full bg-gradient-to-br from-red-500 to-red-600 px-1 text-xs font-bold text-white shadow-lg ring-2 ring-white">
                    {unreadCount > 99 ? (
                      <span className="text-[0.6rem] leading-none">99+</span>
                    ) : (
                      <span className="leading-none">{unreadCount}</span>
                    )}
                  </div>
                )}
              </Button>
            )}

            <Tooltip
              anchorSelect={`#ancho`}
              place="left"
              style={{
                color: '#FFF',
                fontFamily: 'Poppins',
                fontSize: '14px',
                fontStyle: 'normal',
                fontWeight: '400',
                lineHeight: 'normal',
                borderRadius: '8px',
                backgroundColor: '#000',
                opacity: '0.8',
                display: 'inline-flex',
                padding: '9px 16px',
                justifyContent: 'flex-start',
                alignItems: 'center',
                gap: '10px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                zIndex: '9999',
              }}
              content="Activity Log"
            />
            {activeTab === '1' && dataDetails?.status !== 'ARCHIVED' && (
              <AddTaskModalTaskoverview
                addTaskModalOpen={addTaskModalOpen}
                handleSubmit={(data: AddTaskModalTaskOverViewFormData) => handleAddTask(data)}
                departments={departments}
                setAddTaskModalOpen={() => {
                  if (!dataDetails?.business_unit) {
                    toast.error('Please select business unit first.');
                  } else {
                    setAddTaskModalOpen(!addTaskModalOpen);
                  }
                }}
                groupId={dataDetails?.business_unit || ''}
              />
            )}

            {/* {activeTab === '1' && (
              <Button className="inline-flex items-center justify-start gap-2.5 rounded-lg bg-custom-primary px-5 py-2.5 font-['Poppins'] text-xs font-medium text-white hover:bg-custom-primary hover:text-white">
                Send Acknowledgment
              </Button>
            } */}
          </div>
        </div>
        {/* Rendering Table Conditionally */}
        <div
          className={`mt-2 overflow-auto rounded-lg bg-primary-background ${styles.table_main_content}`}
          style={{
            height: 'calc(100vh - 290px )',
          }}
        >
          {activeTab === '1' && (
            <TaskDataTable
              data={taskTableData}
              columns={TaskDataTableColumns}
              loading={false}
              handleUpdate={handleUpdate}
              setShowTaskView={setShowTaskView}
              showTaskView={showTaskView}
              userData={[]}
              departments={departments}
              groupId={dataDetails?.business_unit || ''}
              requestStatus={dataDetails?.status}
            />
          )}
          {activeTab === '2' && (
            <div className="p-2">
              <div className="flex justify-end">
                <Button
                  className="cursor-pointer outline-none"
                  onClick={handleOpenAuditLogPanel}
                  id={`anchor_elementss_`}
                >
                  <Maximize2 />
                </Button>
                <Tooltip
                  anchorSelect={`#anchor_elementss_`}
                  place="bottom"
                  style={{
                    color: '#FFF',
                    fontFamily: 'Poppins',
                    fontSize: '14px',
                    fontStyle: 'normal',
                    fontWeight: '400',
                    lineHeight: 'normal',
                    borderRadius: '8px',
                    backgroundColor: '#000',
                    opacity: '0.8',
                    display: 'inline-flex',
                    padding: '9px 16px',
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                    gap: '10px',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    zIndex: '9999',
                  }}
                  content="View Audit Log"
                />
              </div>
              <div className="flex size-full shrink-0 flex-col overflow-auto scrollbar-hide">
                {auditLogData?.result?.length > 0 ? (
                  auditLogData?.result?.map((log: any, logIndex: any) => {
                    const isChatAction = ['chat', 'message'].some((keyword) =>
                      log?.action?.toLowerCase().includes(keyword)
                    );

                    return (
                      <div key={logIndex} className="flex w-full flex-row items-center gap-3 p-3">
                        <div className="relative w-full">
                          {isChatAction && (
                            <div className="absolute -top-2 left-[-8] bg-white p-1">
                              <MessagesSquare size={16} className="text-blue-500" />
                            </div>
                          )}
                          <div
                            className={`flex min-h-[6vh] w-full cursor-default flex-row items-center justify-between rounded-lg border border-[#C0CDE0] bg-white p-3 text-base leading-normal text-[#999]`}
                          >
                            <div className="flex items-center gap-1">
                              <AvatarFrame
                                value={`${log?.User?.firstName}${log?.User?.lastName ? ` ${log?.User?.lastName}` : ''}`}
                                getInitials={getInitialsByName}
                              />
                              <div className="flex flex-col">
                                <span className="font-medium">{log?.User?.firstName}</span>
                              </div>
                            </div>
                            <p className="w-3/5 font-medium">
                              {log?.action
                                ?.replace(/_/g, ' ')
                                .replace(/\b\w/g, (letter: string) => letter)}
                            </p>
                            <div className="flex flex-row justify-between gap-2">
                              <div className="flex w-1/2 items-center justify-center gap-2.5 whitespace-nowrap p-2.5">
                                <img src={greyCircle} alt="Grey Circle Dot" />
                                {convertDateToHumanView(log?.createdAt)}
                              </div>
                              <div className="flex w-1/2 items-center justify-center gap-2.5 whitespace-nowrap p-2.5">
                                <img src={greyCircle} alt="Grey Circle Dot" />
                                {new Date(log?.createdAt).toLocaleTimeString('en-US', {
                                  hour: '2-digit',
                                  minute: '2-digit',
                                })}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="flex w-full flex-row items-center justify-center p-3 text-base leading-normal text-[#999]">
                    No audit log available
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        <div className="sticky bottom-0 mt-2 flex w-full flex-row justify-between rounded-md bg-white p-1">
          <Button
            className="border-bg-custom-primary text-bg-custom-primary border bg-custom-primary/30 font-['Poppins'] text-xs font-medium hover:bg-custom-primary/30"
            onClick={handlePreviousActiveStep}
            variant="secondary"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="18"
              viewBox="0 0 20 21"
              fill="none"
            >
              <path
                d="M7.98438 15.1953L2.92604 10.137L7.98438 5.07865"
                stroke="#132650"
                strokeWidth="1.5"
                strokeMiterlimit="10"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M17.0938 10.1367L3.06875 10.1367"
                stroke="#132650"
                strokeWidth="1.5"
                strokeMiterlimit="10"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
            </svg>
            Prev Step
          </Button>
          <Button
            className="content-end justify-end bg-custom-primary font-['Poppins'] text-xs font-medium text-white hover:bg-custom-primary"
            onClick={isLastStep ? handleRequestComplete : handleNextStep}
            disabled={
              (taskTableData?.length > 0 &&
                taskTableData.find((row: any) => row?.progress !== 'COMPLETED')) ||
              (dataDetails?.status === 'COMPLETED' && isLastStep) ||
              dataDetails?.status === 'PENDING' ||
              dataDetails?.status === 'ARCHIVED'
            }
          >
            {isLastStep ? (
              dataDetails?.status === 'COMPLETED' ? (
                <>Mark as Completed</>
              ) : (
                <>
                  Mark as Complete
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    className="ml-2"
                  >
                    <path
                      d="M16.6663 5L7.49967 14.1667L3.33301 10"
                      stroke="white"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </>
              )
            ) : (
              <>
                Next Step
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="18"
                  viewBox="0 0 20 21"
                  fill="none"
                >
                  <path
                    d="M12.0352 5.07812L17.0935 10.1365L12.0352 15.1948"
                    stroke="white"
                    strokeWidth="1.5"
                    strokeMiterlimit="10"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M2.92578 10.1367H16.9508"
                    stroke="white"
                    strokeWidth="1.5"
                    strokeMiterlimit="10"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Acknowledgment;
