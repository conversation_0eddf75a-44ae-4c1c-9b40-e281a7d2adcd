{"name": "go_trust", "version": "7.0.1", "private": true, "homepage": "/", "scripts": {"build": "cross-env VITE_APP_VERSION=$npm_package_version tsc && vite build", "fix": "cross-env ESLINT_CACHE=true eslint . --cache --ext .tsx --fix ; prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "preview": "vite preview --port 3000", "release": "release-it --ci", "sentry:sourcemaps": "node scripts/upload-sourcemaps.js", "sentry:sourcemaps:direct": "npx sentry-cli sourcemaps inject ./dist && npx sentry-cli sourcemaps upload --release=$npm_package_version ./dist", "sentry:sourcemaps:legacy": "npx sentry-cli releases files $npm_package_version upload-sourcemaps inject --org privacy --project frontend ./dist --validate", "sentry:info": "npx sentry-cli info", "sentry:orgs": "npx sentry-cli organizations list", "sentry:projects": "npx sentry-cli projects list", "start": "cross-env VITE_APP_VERSION=$npm_package_version vite", "prepare": "husky && husky install"}, "engines": {"node": "^22.14.0", "npm": "^10.9.2"}, "overrides": {"cross-spawn": "7.0.5", "http-cache-semantics": "4.1.1", "semver-regex": "4.0.1", "trim-newlines": "4.1.1", "d3-color": "3.1.0", "react": "19.0.0", "react-dom": "19.0.0", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "eslint": "^9.22.0"}, "lint-staged": {"*.{json,md,html}": ["npx prettier --write"], "*.{css,scss}": ["npx prettier --write"], "*.{js,jsx,ts,tsx}": ["npx prettier --write"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hello-pangea/dnd": "^16.6.0", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.6", "@reduxjs/toolkit": "^2.5.0", "@sentry/browser": "^9.29.0", "@sentry/cli": "^2.46.0", "@sentry/react": "^9.29.0", "@sentry/tracing": "^7.120.3", "@tanstack/react-query": "^4.36.1", "@tanstack/react-query-devtools": "^4.36.1", "@tanstack/react-table": "^8.20.6", "@tiptap/core": "2.11.7", "@tiptap/extension-bullet-list": "2.11.7", "@tiptap/extension-color": "2.11.7", "@tiptap/extension-floating-menu": "2.11.7", "@tiptap/extension-font-family": "2.11.7", "@tiptap/extension-highlight": "2.11.7", "@tiptap/extension-link": "2.11.7", "@tiptap/extension-list-item": "2.11.7", "@tiptap/extension-ordered-list": "2.11.7", "@tiptap/extension-placeholder": "2.11.7", "@tiptap/extension-table": "2.11.7", "@tiptap/extension-table-cell": "2.11.7", "@tiptap/extension-table-header": "2.11.7", "@tiptap/extension-table-row": "2.11.7", "@tiptap/extension-task-item": "2.11.7", "@tiptap/extension-task-list": "2.11.7", "@tiptap/extension-text-align": "2.11.7", "@tiptap/extension-text-style": "2.11.7", "@tiptap/extension-underline": "2.11.7", "@tiptap/extension-horizontal-rule": "2.11.7", "@tiptap/react": "2.11.7", "@tiptap/starter-kit": "2.11.7", "@typescript-eslint/eslint-plugin": "^8.25.0", "@typescript-eslint/parser": "^8.25.0", "add": "^2.0.6", "axios": "^1.7.9", "babel-loader": "^10.0.0", "chart.js": "^4.4.7", "chartjs-chart-matrix": "^2.0.1", "chartjs-plugin-datalabels": "^2.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "crypto-js": "^4.2.0", "css-loader": "^7.1.2", "d3": "^7.9.0", "d3-geo": "^3.1.1", "d3-sankey": "^0.12.3", "d3-selection": "^3.0.0", "dagre": "^0.8.5", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "dompurify": "^3.2.3", "dotenv": "^16.4.7", "elkjs": "^0.10.0", "framer-motion": "^11.17.0", "i18next": "^23.16.8", "install": "^0.13.0", "js-cookie": "^3.0.5", "libphonenumber-js": "^1.11.17", "lodash": "^4.17.21", "lucide-react": "^0.471.2", "moment": "^2.30.1", "node-forge": "^1.3.1", "oidc-client-ts": "^3.1.0", "popover": "^2.4.1", "postcss-loader": "^8.1.1", "prosemirror-model": "^1.24.1", "react": "19.0.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-color": "^2.19.3", "react-color-palette": "^7.3.0", "react-d3-tree": "^3.6.2", "react-datepicker": "^7.6.0", "react-day-picker": "^8.10.1", "react-device-detect": "^2.2.3", "react-dom": "19.0.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.1", "react-i18next": "^15.4.0", "react-icons": "^5.4.0", "react-infinite-scroller": "^1.2.6", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "react-modal": "^3.16.3", "react-oidc-context": "^3.2.0", "react-password-strength-bar": "^0.4.1", "react-pdf": "^9.2.1", "react-phone-number-input": "^3.4.11", "react-player": "^2.16.0", "react-redux": "^9.2.0", "react-responsive": "^10.0.0", "react-responsive-carousel": "^3.2.23", "react-ribbons": "^1.1.7", "react-router-dom": "^6.28.1", "react-simple-maps": "^3.0.0", "react-tooltip": "^5.28.0", "react-window": "^1.8.11", "reactflow": "^11.11.4", "recharts": "^2.15.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "redux-thunk": "^3.1.0", "remixicon": "^4.6.0", "shadcn-ui": "^0.9.4", "style-loader": "^4.0.0", "styled-components": "^6.1.14", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "ts-loader": "^9.5.2", "web-vitals": "^4.2.4", "zod": "^3.24.1"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/compat": "^1.2.4", "@eslint/js": "^9.18.0", "@release-it/conventional-changelog": "^8.0.2", "@tanstack/eslint-plugin-query": "^5.66.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/crypto-js": "^4.2.2", "@types/d3": "^7.4.3", "@types/d3-sankey": "^0.12.4", "@types/dagre": "^0.7.52", "@types/eslint__js": "^8.42.3", "@types/geojson": "^7946.0.15", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.14", "@types/node": "^20.17.12", "@types/node-forge": "^1.3.11", "@types/react": "^19.0.2", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-color": "^3.0.13", "@types/react-dom": "^19.0.2", "@types/react-infinite-scroller": "^1.2.5", "@types/react-modal": "^3.16.3", "@types/react-redux": "^7.1.34", "@types/react-router-dom": "^5.3.3", "@types/react-simple-maps": "^3.0.6", "@types/react-window": "^1.8.8", "@types/redux-thunk": "^2.1.32", "@types/styled-components": "^5.1.34", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "eslint": "^9.22.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-no-secrets": "^1.1.2", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-sonarjs": "^2.0.4", "eslint-plugin-tailwindcss": "^3.17.5", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^8.0.3", "lint-staged": "^15.3.0", "postcss": "^8.4.49", "postcss-nested": "^6.2.0", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.9", "release-it": "^17.11.0", "resolve-cwd": "^3.0.0", "tailwindcss": "^3.4.17", "typescript": "^5.7.3", "typescript-eslint": "^8.19.1", "vite": "^6.2.0", "vite-plugin-imagemin": "^0.6.1", "webpack": "^5.99.8", "webpack-cli": "^6.0.1"}, "release-it": {"$schema": "https://unpkg.com/release-it/schema/release-it.json", "plugins": {"@release-it/conventional-changelog": {"preset": "angular"}}, "github": {"release": true}, "git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}", "requireCleanWorkingDir": true, "push": true}, "npm": {"publish": true, "ignoreVersion": false}}}